import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import js from '@eslint/js';
import { includeIgnoreFile } from '@eslint/compat';

import ts from 'typescript-eslint';
import vueParser from 'vue-eslint-parser';
import vue from 'eslint-plugin-vue';
import tailwind from 'eslint-plugin-tailwindcss';
import prettierConfig from '@vue/eslint-config-prettier';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const gitignorePath = path.resolve(__dirname, '.gitignore');

export default [
  js.configs.recommended,
  ...ts.configs.recommended,
  ...tailwind.configs['flat/recommended'],
  prettierConfig,

  {
    files: ['**/*.{ts,tsx,vue}'],
    languageOptions: {
      parser: ts.parser,
    },
  },

  {
    files: ['**/*.{js,ts,jsx,tsx,vue}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {},
  },

  {
    files: ['**/*.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: ts.parser, // parse TS inside VUE
      },
    },
  },

  includeIgnoreFile(gitignorePath),

  {
    plugins: {
      vue,
      '@typescript-eslint': ts.plugin,
      tailwindcss: tailwind,
    },

    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',
      },
    },

    settings: {
      tailwindcss: {
        config: path.join(__dirname, 'tailwind.config.js'),
      },
    },

    files: ['**/*.ts', '**/*.vue'], // -- ext .ts,vue

    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-empty-function': 'off',

      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
        },
      ],
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          args: 'none',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/no-empty-object-type': [
        'error',
        {
          allowInterfaces: 'with-single-extends',
        },
      ],

      'vue/multi-word-component-names': 'off',
      'vue/no-dupe-keys':
        process.env.NODE_ENV !== 'production' ? 'warn' : 'off',
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-var': 'error',
      'comma-dangle': ['error', 'only-multiline'],

      'id-length': [
        'warn',
        {
          min: 2,
          exceptions: ['i', 'j', '_', 'e', 'h'],
        },
      ],

      'prettier/prettier': [
        'error',
        {
          endOfLine: 'auto',
        },
      ],

      'tailwindcss/no-custom-classname': [
        'error',
        {
          whitelist: [
            'btn',
            'btn\\-.*',
            'aventur-logo',
            '(bg|text|outline|border|shadow|ring|ring-offset)\\-(primary|secondary).*',
            'text-xxs',
            'animate-gradient',
          ],
        },
      ],

      'sort-imports': [
        'warn',
        {
          ignoreCase: false,
          ignoreDeclarationSort: true,
          ignoreMemberSort: false,
          memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
          allowSeparatedGroups: true,
        },
      ],
    },
  },

  // to ignore jest.expect() calls
  {
    files: ['**/*.test.ts'],
    rules: {
      '@typescript-eslint/no-unused-expressions': 'off',
    },
  },

  {
    files: ['**/*.config.mjs', '**/.prettierrc.js'],
    rules: {
      'no-undef': 'off',
    },
  },
];
