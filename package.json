{"name": "aventur-core", "version": "0.0.1", "scripts": {"test": "vitest run", "coverage": "vitest run --coverage", "tsc": "vue-tsc --noEmit", "lint": "eslint ./src", "lint:debug": "eslint ./src --debug", "lint:fix": "eslint ./src --fix", "lint:inspect-config": "eslint --inspect-config", "prettier": "prettier -w -u ./src", "tailwind-config-viewer": "tailwind-config-viewer -o"}, "engines": {"node": ">=22.17"}, "dependencies": {"@amplitude/analytics-browser": "^2.21.0", "@aws-amplify/ui-vue": "^4.3.3", "@casl/ability": "^6.7.3", "@casl/vue": "^2.2.2", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@sentry/tracing": "^7.120.3", "@vee-validate/rules": "^4.15.1", "@vueform/multiselect": "^2.6.11", "@vueuse/components": "^13.5.0", "@vueuse/core": "^13.5.0", "@vueuse/router": "^13.5.0", "ably": "^2.10.1", "aws-amplify": "^6.15.3", "change-case-all": "^2.1.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "luxon": "^3.7.1", "maska": "^3.2.0", "material-icons": "^1.13.14", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "qs": "^6.14.0", "sortablejs": "^1.15.6", "sortablejs-vue3": "^1.2.11", "swiper": "^11.2.10", "uuid": "^11.1.0", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.5.1", "vue-slider-component": "^4.1.0-beta.7", "vue-tippy": "^6.7.1", "vue-toastification": "^2.0.0-rc.5", "vuejs-confirm-dialog": "^0.5.2", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@faker-js/faker": "^9.9.0", "@pinia/testing": "^1.0.2", "@sentry/vite-plugin": "^3.6.0", "@sentry/vue": "^9.39.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.20", "@types/luxon": "^3.6.2", "@types/node": "^22.16.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/coverage-v8": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "c8": "^10.1.3", "eslint": "^9.31.0", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "jsdom": "^26.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "prettier": "^3.6.2", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "sinon": "^21.0.0", "tailwind-config-viewer": "^2.0.4", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vite": "^7.0.4", "vite-plugin-pwa": "^1.0.1", "vitest": "^3.2.4", "vue-eslint-parser": "^10.2.0", "vue-loader": "^17.4.2", "vue-tsc": "^3.0.1"}}