import { Nullable, Options } from 'src/types/Common';

export type RefDataDTO = Record<string, any>;
export interface OptionsRefDataDTO extends Options<number> {}
export interface SelectOptionRefDataDTO {
  value: string;
  label: string;
}
export type EnumRefDataDTO = Array<string | number>;

export interface Provider extends OptionsRefDataDTO {
  is_active: boolean | null;
}

export interface Goal extends OptionsRefDataDTO {
  description: string;
  client_goal: boolean;
  code: string;
  is_default: boolean;
}

export interface Cashflow extends OptionsRefDataDTO {
  income_expenditure_group: {
    id: number;
    type: 'Income' | 'Expenditure';
    name: string;
    description: string;
  };
  is_active: boolean;
  is_essential: Nullable<boolean>;
}

export interface ProductType extends OptionsRefDataDTO {
  product_type_group_id: number;
  type:
    | 'account'
    | 'property'
    | 'company_shares'
    | 'crypto_currency'
    | 'other_asset'
    | 'mortgage'
    | 'personal_loan'
    | 'credit_card'
    | 'other_debt';
  data_layout:
    | 'pension_layout'
    | 'protection_layout'
    | 'investment_layout'
    | 'pension_income_paying_layout'
    | null;
}

export interface ProductTypeGroup extends OptionsRefDataDTO {
  asset_or_debt: 'asset' | 'debt';
  compatible_advice_types: number[];
}

export interface AdviceType extends OptionsRefDataDTO {
  existing: boolean;
  proposed: boolean;
  requires_amount: boolean;
  requires_frequency: boolean;
  requires_portfolio: boolean;
  requires_account: boolean;
}

export interface AdviceGroup extends OptionsRefDataDTO {
  advice_types_ids: AdviceType['id'][];
  product_type_group_id: ProductTypeGroup['id'];
}

export interface Country extends OptionsRefDataDTO {
  iso_alpha2: string;
  iso_alpha3: string;
}

export interface ClientSource extends OptionsRefDataDTO {
  source_group: string;
}

export interface NoEmailReason extends OptionsRefDataDTO {
  reason: string;
}

export interface AdvisorRole extends SelectOptionRefDataDTO {}

export interface DocumentTemplateTypeDTO extends EnumRefDataDTO {}

export type RefDataBlobDTO = {
  countries: RefDataDTO[];
  nationalities: RefDataDTO[];
  genders: RefDataDTO[];
  goals: RefDataDTO[];
  providers: RefDataDTO[];
  cashflows: RefDataDTO[];
  product_types: RefDataDTO[];
  product_type_groups: RefDataDTO[];
  advice_types: RefDataDTO[];
  advice_groups: RefDataDTO[];
  personal_titles: RefDataDTO[];
  marital_statuses: RefDataDTO[];
  relationship_types: RefDataDTO[];
  client_sources: RefDataDTO[];
  client_statuses: RefDataDTO[];
  client_types: RefDataDTO[];
  portfolio_models: RefDataDTO[];
  holding_statuses: RefDataDTO[];
  no_email_reasons: RefDataDTO[];
  assignee_groups: RefDataDTO[];
  documentTemplateTypes: RefDataDTO[];
  risk_level: RefDataDTO[];
};
