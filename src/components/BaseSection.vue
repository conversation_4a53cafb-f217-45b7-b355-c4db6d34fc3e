<template>
  <section
    :class="[
      'group',
      {
        'px-7': !(noPadding || noPaddingX),
        'py-5': !(noPadding || noPaddingY),
      },
      props.class,
      dividerClass,
    ]"
  >
    <BaseTitle v-if="!!title">
      {{ title }}
    </BaseTitle>
    <slot />
  </section>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import BaseTitle from 'src/components/BaseTitle.vue';

  const props = defineProps<{
    divider?: 'top' | 'bottom';
    class?: string;
    title?: string;
    noPadding?: boolean;
    noPaddingY?: boolean;
    noPaddingX?: boolean;
  }>();

  const dividerClass = computed(() => ({
    'border-gray-200': props.divider,
    'border-t': props.divider === 'top',
    'border-b': props.divider === 'bottom',
  }));
</script>
