<template>
  <div class="relative inline-block w-full">
    <transition
      enter-active-class="transition duration-300"
      enter-from-class="opacity-50"
      leave-active-class="transition duration-300"
      leave-to-class="opacity-0"
    >
      <div
        v-if="props.isLoading"
        class="absolute inset-0 z-50 flex justify-center bg-white/50 backdrop-blur-sm"
      >
        <LoadingSpinner class="mt-5 size-10" />
      </div>
    </transition>
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
  import LoadingSpinner from 'src/components/LoadingSpinner.vue';
  const props = defineProps({
    isLoading: {
      type: Boolean,
      required: true,
      default: false,
    },
  });
</script>
