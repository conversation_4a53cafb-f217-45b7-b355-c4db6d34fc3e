<template>
  <div>
    <slot name="open-button" :open="modalApi.open" :close="modalApi.close" />
    <TransitionRoot as="template" :show="isOpened" class="inset-0">
      <Dialog
        class="relative z-50"
        as="div"
        :initial-focus="config.initialFocus"
        @close="config.outsideClickClose ? modalApi.close() : undefined"
      >
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black/30" />
        </TransitionChild>

        <TransitionChild
          as="div"
          class="fixed inset-0 flex items-center justify-center p-4"
          enter="ease-out duration-300"
          enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leave-from="opacity-100 translate-y-0 sm:scale-100"
          leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <!-- Full-screen scrollable container -->
          <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
              <DialogPanel class="w-full max-w-lg rounded">
                <slot :open="modalApi.open" :close="modalApi.close" />
              </DialogPanel>
            </div>
          </div>
        </TransitionChild>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import {
    Dialog,
    DialogPanel,
    TransitionChild,
    TransitionRoot,
  } from '@headlessui/vue';
  import { ModalConfig, defaultConfig } from 'src/types/Modal';

  const props = defineProps<{
    config?: Partial<ModalConfig>;
  }>();

  const emit = defineEmits(['use-api']);

  const config = computed(() => {
    return Object.assign({}, defaultConfig, props.config || {});
  });

  const isOpened = ref(config.value.isOpened);

  const modalApi = {
    open: () => (isOpened.value = true),

    close: () => (isOpened.value = false),
  };

  onMounted(() => {
    emit('use-api', modalApi);
  });
</script>
