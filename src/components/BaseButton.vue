<template>
  <button
    :type="type || 'button'"
    class="whitespace-normal rounded-md px-5 py-2 transition-opacity focus:outline focus:outline-gray-100"
    :class="[
      props.class,
      {
        'opacity-50': isDisabled,
        'hover:opacity-90': !isDisabled,
      },
      themeClass(theme || 'primary'),
    ]"
    :disabled="isDisabled"
    @click="(...e) => $emit('on-click', ...e)"
  >
    <span v-if="isBusy" class="flex justify-center">
      <LoadingSpinner class="size-6" />
    </span>
    <template v-else>
      <slot />
    </template>
  </button>
</template>

<script setup lang="ts">
  import { VueClassProp } from 'src/types/Html';
  import { computed } from 'vue';
  import LoadingSpinner from 'src/components/LoadingSpinner.vue';

  export type Theme =
    | 'primary'
    | 'primary-ghost'
    | 'gray-ghost'
    | 'secondary'
    | 'text-like'
    | 'text-like-secondary'
    | 'custom';

  const props = defineProps<{
    disabled?: boolean;
    theme: Theme;
    type?: 'button' | 'submit';
    class?: VueClassProp;
    isBusy?: boolean;
  }>();

  defineEmits(['on-click']);

  const isDisabled = computed(() => props.disabled || props.isBusy);

  const themeClass = (theme: Theme): string => {
    const themeMap = new Map<Theme, string>([
      ['primary', `bg-primary text-white`],
      [
        'primary-ghost',
        `transition-colors bg-white text-primary hover:bg-primary-100`,
      ],
      [
        'gray-ghost',
        `transition-colors bg-white text-gray-400 hover:bg-gray-100`,
      ],
      [
        'secondary',
        `transition-colors bg-primary-50 border text-secondary-700 border-secondary-700 hover:bg-secondary-200`,
      ],
      [
        'text-like',
        `transition-colors border border-none text-gray-500 underline focus:underline focus:decoration-dashed focus:outline-0`,
      ],
      [
        'text-like-secondary',
        `transition-colors border border-none text-secondary-700 underline`,
      ],
      ['custom', ``],
    ]);

    return themeMap.get(theme) || '';
  };
</script>
