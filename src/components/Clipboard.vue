<template>
  <div v-if="isSupported">
    <slot :copy="onCopy" :copied="copied" :text="text">
      <button @click.prevent="onCopy">
        <span v-if="source && !copied">Copy</span>
        <span v-else-if="text">Copied!</span>
      </button>
    </slot>
  </div>
</template>

<script setup lang="ts">
  import { useClipboard } from '@vueuse/core';
  import { Maybe } from 'src/types/Common';

  const props = defineProps<{
    source: Maybe<string>;
  }>();

  const { text, copy, copied, isSupported } = useClipboard({
    source: props.source || '',
  });

  const onCopy = () => {
    props.source && copy(props.source);
  };
</script>
