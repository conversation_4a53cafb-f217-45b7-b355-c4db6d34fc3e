<template>
  <input
    :id="props.name"
    :value="modelValue"
    :type="type"
    :name="name"
    :autocomplete="autocomplete"
    :disabled="disabled"
    :placeholder="placeholder"
    class="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border-gray-300 py-2 shadow-sm disabled:bg-gray-100 disabled:text-gray-400 disabled:shadow-none sm:text-sm lg:text-base"
    :class="{
      'border-red-600': !!errorMessage,
      'text-red-600': !!errorMessage,
      'cursor-not-allowed bg-gray-100 text-gray-400': !!disabled,
    }"
    :readonly="isReadonly"
    @blur="handleBlur"
    @input="handleInput"
  />
</template>

<script setup lang="ts">
  const props = defineProps<{
    id: string;
    type: string;
    value: string | number | null;
    modelValue: string | number | null;
    autocomplete?: string;
    name: string;
    placeholder?: string;
    disabled?: boolean;
    isRequired?: boolean;
    errorMessage?: string;
    isReadonly?: boolean;
  }>();

  const emit = defineEmits(['update:modelValue', 'blur']);
  const handleBlur = (ev: Event) => {
    emit('blur', ev);
  };

  const handleInput = (event: Event) => {
    emit('update:modelValue', (event.target as HTMLInputElement).value);
  };
</script>
