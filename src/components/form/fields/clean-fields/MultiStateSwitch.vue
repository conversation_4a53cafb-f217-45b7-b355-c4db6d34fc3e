<template>
  <input-wrapper
    :name="props.name"
    :label="label || ''"
    :error-message="errorMessage"
  >
    <div>
      <span v-for="(option, key) in options" :key="key" class="group">
        <input
          :id="`${name}-${option.value.toString()}`"
          type="radio"
          class="peer hidden"
          :name="name"
          :checked="modelValue === option.value"
          :value="modelValue"
          :disabled="isDisabled"
        />
        <label
          :for="`${name}-${option.value.toString()}`"
          class="text-primary inline-block items-center border border-r-0 px-4 py-1.5 text-sm group-first:rounded-l-md group-last:rounded-r-md group-last-of-type:border-r group-only-of-type:border peer-checked:font-medium peer-checked:text-white"
          :class="{
            'border-primary/50 text-primary/50 peer-checked:bg-primary/30 cursor-not-allowed peer-checked:text-white/50':
              isDisabled,
            'border-primary-950 peer-checked:bg-primary cursor-pointer':
              !isDisabled,
            'peer-checked:bg-primary/60': !modelValue,
          }"
          @click="() => handleChange(option)()"
        >
          {{ option.label }}
        </label>
      </span>
    </div>
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { SelectOption } from '../field-model';
  import InputWrapper from '../InputWrapper.vue';

  const props = defineProps<{
    options: SelectOption[];
    name: string;
    disabled?: boolean;
    modelValue?: SelectOption['value'];
    errorMessage?: string;
    label?: string;
  }>();

  const name = toRef(props, 'name');
  const isDisabled = toRef(props, 'disabled');

  const emit = defineEmits(['update:modelValue']);

  function handleChange(option: SelectOption) {
    return () => {
      emit('update:modelValue', option.value);
    };
  }
</script>
