<template>
  <input
    :id="String(modelValue)"
    :name="name"
    type="checkbox"
    :value="modelValue"
    class="text-primary focus:ring-primary size-4"
    :checked="checked"
    @change="(e) => $emit('change', e)"
    @blur="(e) => $emit('blur', e)"
  />
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { SelectOption } from '../field-model';

  const props = defineProps<{
    name: string;
    modelValue: SelectOption['value'];
    checked: boolean;
    disabled?: boolean;
  }>();
  defineEmits(['change', 'blur']);

  const name = toRef(props, 'name');
</script>
