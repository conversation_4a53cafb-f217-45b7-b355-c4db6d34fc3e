<template>
  <input-wrapper
    :name="props.name"
    :label="label"
    :error-message="errorMessage"
  >
    <div class="flex flex-row flex-wrap gap-5">
      <div
        v-for="option in options"
        :key="String(option.value)"
        class="flex items-center gap-2"
      >
        <input
          :id="`${props.name}-${String(option.value)}`"
          :name="props.name"
          type="radio"
          :value="option.value"
          :checked="option.value === inputValue"
          class="text-primary size-4 focus:ring-2 focus:ring-teal-900"
          :class="{
            'border-red-600': !!errorMessage,
            'text-red-600': !!errorMessage,
          }"
          @input="handleChange"
          @blur="handleBlur"
        />
        <label
          :for="`${props.name}-${String(option.value)}`"
          class="m:text-sm text-gray-700 hover:cursor-pointer lg:text-base"
        >
          {{ option.label }}
        </label>
      </div>
    </div>
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';
  import { SelectOption } from './field-model';

  const props = withDefaults(
    defineProps<{
      label: string;
      value?: string;
      name: string;
      options: SelectOption[];
    }>(),
    {
      value: '',
    },
  );

  const name = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
  } = useField(name, undefined, {});
</script>
