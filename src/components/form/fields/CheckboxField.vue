<template>
  <input-wrapper
    :name="name"
    :label="label"
    :error-message="errorMessage"
    :is-horizontal="true"
    :justify-between="!!attrs.justifyBetween"
  >
    <input
      :id="String(checkedValue)"
      :name="name"
      type="checkbox"
      :value="checkedValue"
      class="text-primary focus:ring-primary size-4"
      :class="{
        'border-red-600': !!errorMessage,
        'text-red-600': !!errorMessage,
        '!border-gray-300 !bg-gray-300/80': isDisabled,
      }"
      :disabled="isDisabled"
      :checked="checked"
      @change="handleChange"
      @blur="handleBlur"
    />
  </input-wrapper>
</template>

<script setup lang="ts">
  import { computed, toRef, useAttrs } from 'vue';
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';

  const attrs = useAttrs();
  const props = defineProps<{
    name: string;
    label: string;
    initiallyChecked?: boolean;
    checkedValue: boolean;
  }>();

  const isDisabled = computed(() => !!attrs.disabled);

  const name = toRef(props, 'name');
  const checkedValue = toRef(props, 'checkedValue');

  const { checked, errorMessage, handleBlur, handleChange } = useField(
    name,
    undefined,
    {
      type: 'checkbox',
      checkedValue,
      uncheckedValue: false,
      initialValue: props.initiallyChecked,
    },
  );
</script>
