<template>
  <input-wrapper
    :name="props.name"
    :label="label"
    :error-message="errorMessage"
    content-class="flex justify-end"
    :is-disabled="isDisabled"
    :is-horizontal="true"
    :justify-between="true"
  >
    <label
      class="relative inline-flex items-center"
      :class="{
        'cursor-not-allowed': isDisabled,
        'cursor-pointer': !isDisabled,
      }"
    >
      <input
        :id="name"
        type="checkbox"
        class="peer sr-only"
        :name="name"
        :value="value"
        :checked="checked"
        :disabled="isDisabled"
        @change="
          (e) => {
            handleChange(e);
            $emit('on-toggle', !!checked);
          }
        "
        @blur="handleBlur"
      />
      <div
        class="peer-checked:bg-primary peer-disabled:peer-checked:bg-primary/30 peer h-6 w-11 rounded-full bg-gray-300/80 shadow-[inset_0_1px_2px_rgba(0,0,0,0.15),0_1px_0_rgba(255,255,255,0.2)] after:absolute after:left-[2px] after:top-[2px] after:size-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-teal-600 peer-disabled:bg-gray-200 dark:peer-focus:ring-teal-600"
      />
    </label>
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef } from 'vue';
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';

  const props = defineProps<{
    name: string;
    label: string;
    value: boolean;
    disabled?: boolean;
  }>();

  defineEmits<{
    (e: 'on-toggle', isChecked: boolean): void;
  }>();

  const name = toRef(props, 'name');
  const value = toRef(props, 'value');
  const isDisabled = toRef(props, 'disabled');

  const { checked, errorMessage, handleBlur, handleChange } = useField(
    name,
    undefined,
    {
      type: 'checkbox',
      checkedValue: value,
      uncheckedValue: false,
    },
  );
</script>
