<template>
  <input-wrapper
    :name="props.name"
    :label="label"
    :error-message="errorMessage"
  >
    <input
      :id="props.name"
      :value="inputValue"
      type="date"
      :name="props.name"
      max="9999-12-12"
      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm lg:text-base"
      :class="{
        'cursor-not-allowed bg-gray-100 text-gray-400': props.disabled,
        'border-red-600 text-red-600': !!errorMessage,
      }"
      :disabled="props.disabled"
      @input="debouncedHandleInput"
    />
    <span
      v-if="props.hint && !errorMessage"
      class="flex py-1 text-xs leading-tight text-gray-500"
      >Hint: {{ hint }}</span
    >
  </input-wrapper>
</template>

<script setup lang="ts">
  import { toRef, watch } from 'vue';
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';
  import { debounce } from 'lodash';
  import { DateTime } from 'src/utils/dateTime';

  const props = withDefaults(
    defineProps<{
      name: string;
      value?: string;
      label: string;
      hint?: string;
      disabled?: boolean;
      required?: boolean;
    }>(),
    {
      value: undefined,
      disabled: false,
      required: false,
    },
  );

  const emit = defineEmits(['on-pick', 'on-failure']);

  const name = toRef(props, 'name');
  const label = toRef(props, 'label');
  const propsValue = toRef(props, 'value');

  const isValid = (value: string) => {
    if (!value) return !props.required;
    const _date = new DateTime(value);
    const min = new DateTime('1900-01-01').valueOf();
    const max = new DateTime('2200-01-01').valueOf();
    if (_date.compare('is-after', max))
      return 'Date must be earlier than 01/01/2200';
    if (_date.compare('is-before', min))
      return 'Date must be later than 01/01/1900';
    return true;
  };

  const {
    value: inputValue,
    meta,
    errorMessage,
    handleChange,
  } = useField(name, (value) => isValid(value), {
    initialValue: propsValue.value,
  });

  watch(propsValue, () => handleChange(propsValue.value));

  const sleep = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));

  const handleInput = async (event: Event) => {
    handleChange(event);
    if (meta.pending) await sleep(100);
    meta.valid ? emit('on-pick', event) : emit('on-failure');
  };

  const debouncedHandleInput = debounce(handleInput, 500);
</script>
