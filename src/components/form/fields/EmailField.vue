<template>
  <input-wrapper
    :name="props.name"
    :label="label"
    :error-message="errorMessage"
  >
    <input
      :id="props.name"
      :value="inputValue"
      type="email"
      :name="props.name"
      :autocomplete="props.autocomplete"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      class="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border-gray-300 shadow-sm disabled:border-slate-200 disabled:bg-gray-50 disabled:text-slate-500 disabled:shadow-none sm:text-sm lg:text-base"
      :class="{
        'border-red-600': !!errorMessage,
        'text-red-600': !!errorMessage,
      }"
      @input="handleChange"
      @blur="handleBlur"
    />
  </input-wrapper>
</template>

<script setup lang="ts">
  import { useField } from 'vee-validate';
  import InputWrapper from './InputWrapper.vue';
  import { toRef } from 'vue';

  const props = defineProps({
    type: {
      type: String,
      default: 'text',
    },
    value: {
      type: String,
      default: '',
    },
    autocomplete: {
      type: String,
      default: '',
      required: false,
    },
    name: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const nameRef = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
  } = useField(nameRef, undefined, {
    initialValue: props.value,
  });

  const label = toRef(props, 'label');
</script>
