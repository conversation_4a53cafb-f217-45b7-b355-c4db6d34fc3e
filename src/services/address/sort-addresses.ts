import { filter, reject, sortBy } from 'lodash';

import { Address } from 'src/modules/factfind/types/Address';

import { formatAddressForView } from './format-address-for-view';
//

export const sortAddresses = <T extends Address>(addresses: T[]) => {
  // If ClientAddress[] is provided the primary address will bubble up,
  // while in case of Address[] there will be just alphabetical ordering
  // due to missing `isPrimary` property
  const primaryAddress = filter(addresses, 'isPrimary');
  return [
    ...primaryAddress,
    ...sortBy(reject(addresses, 'isPrimary'), ($a) => formatAddressForView($a)),
  ];
};
