import { useAsyncState } from '@vueuse/core';
import { debounce, isFunction, isString } from 'lodash';
import * as API from 'aws-amplify/api';
import { RestApiResponse } from '@aws-amplify/api-rest/src/types';
import { DocumentType } from '@aws-amplify/core/internals/utils';

import { isJSON } from 'src/utils/string/isJSON';
import { useAPIState } from 'src/composables/useAPIState';
import { QueryParams, RequestBody } from 'src/types/api';
import { toAPIQueryString } from 'src/services/api';

import { default as utils } from './utils';
import { API_NAME as apiName } from './constants';
import { UnauthorizedError } from './errors';
//

const { setState } = useAPIState();

function catchMiddleware(err: any) {
  if (err.name === 'NetworkError') {
    throw new Error('Internal server error');
  }
  if (err.response?.status === 401) {
    throw new UnauthorizedError(err.message, err.cause);
  }
  throw err;
}

export enum ResponseType {
  Stream = 'stream',
  Text = 'text',
  Blob = 'blob',
  JSON = 'json',
  Bytes = 'bytes',
  ArrayBuffer = 'arrayBuffer',
}

export interface Config {
  responseType?: ResponseType;
}

const devHostReg = /localhost|127.0.0.1|.dev|.local|.test|ngrok/;
const isWatchedRequest = (url: URL) =>
  (devHostReg.test(url.host) || /aventur.co.uk/.test(url.host)) &&
  /\/api\/v\d/.test(url.pathname);

const _setTrueState = () => setState(true);
const _setFalseState = debounce(() => {
  $t && clearTimeout($t);
  $t = window.setTimeout(() => activeRequests.size === 0 && setState(false), 0);
}, 500);

let $t: number;
const activeRequests = new Set();

window.fetch = ((originalFetch) => {
  return (
    ...args: [input: URL | RequestInfo, init?: RequestInit | undefined]
  ) => {
    const url = new URL(args[0] as string);

    if (!isWatchedRequest(url)) {
      return originalFetch.apply(this, args);
    }

    activeRequests.add(url.pathname);
    _setTrueState();
    return originalFetch
      .apply(this, args)
      .then((response) => {
        // status code within the range of 2xx
        activeRequests.delete(url.pathname);
        _setFalseState();
        return response;
      })
      .catch((error) => {
        // status codes that falls outside the range of 2xx
        activeRequests.delete(url.pathname);
        _setFalseState();
        return Promise.reject(error);
      });
  };
})(fetch);

const isEmptyResponse = async (res: RestApiResponse | Response) =>
  res.headers['content-length'] === '0';

const resolvers = {
  stream: (res) => res.body,
  _: (res) => res.body,
};

((res) => {
  ['text', 'blob', 'json', 'stream', 'arrayBuffer', 'bytes'].forEach((type) => {
    !resolvers[type] &&
      (resolvers[type] = isFunction(res[type])
        ? (res) => res[type]()
        : (_) => {
            throw new Error(`Response type '${type}' is not supported`);
          });
  });
})(new Response());

const _resolve = async <R extends DocumentType | unknown | void = DocumentType>(
  response: RestApiResponse | Response,
  resolver: (value: R) => void,
  config?: Config,
) => {
  const status =
    'statusCode' in response ? response.statusCode : response.status;

  if (status === 204 || (await isEmptyResponse(response))) {
    resolver(null as R);
    return;
  }

  if (utils.isReadableStream(response.body)) {
    if (config?.responseType) {
      if (config.responseType === ResponseType.Stream) {
        resolver((await resolvers['stream'](response)) as R);
        return;
      }

      resolver((await resolvers[config.responseType](response.body)) as R);
      return;
    }

    // default to JSON
    resolver((await resolvers['json'](response.body)) as R);
    return;
  }

  /**
   * A fallback to XMLHttpRequest.response parsing in case the underlying request mechanism
   * is not Fetch, but XMLHttpRequest, which isn't the case in the current Amplify API implementation.
   */
  const data =
    isString(response.body) && isJSON(response.body)
      ? JSON.parse(response.body)
      : resolvers['_'](response);

  resolver(data as R);
};

const _get = <R extends DocumentType | unknown | void = DocumentType>(
  path: string,
  queryStringParameters?: QueryParams,
) =>
  new Promise<R>((resolve, reject) =>
    API.get({
      apiName,
      path: path + toAPIQueryString(queryStringParameters),
    })
      .response.then(async (response) => await _resolve<R>(response, resolve))
      .catch(catchMiddleware)
      .catch(reject),
  );

_get.withAsyncState = function <
  R extends DocumentType | unknown | void = DocumentType,
>(path: string, queryStringParameters?: QueryParams) {
  return useAsyncState<R | null>(get(path, queryStringParameters), null);
};

const _post = <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(
  path: string,
  body: B,
  config?: Config,
) =>
  new Promise<R>((resolve, reject) =>
    API.post({
      apiName,
      path,
      options: {
        body,
      },
    })
      .response.then(
        async (response) => await _resolve<R>(response, resolve, config),
      )
      .catch(catchMiddleware)
      .catch(reject),
  );

_post.withAsyncState = function <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(path: string, body: B) {
  return useAsyncState<R | null>(post(path, body), null);
};

const _put = <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(
  path: string,
  body: B,
) =>
  new Promise<R>((resolve, reject) =>
    API.put({
      apiName,
      path,
      options: {
        body,
      },
    })
      .response.then(async (response) => await _resolve<R>(response, resolve))
      .catch(catchMiddleware)
      .catch(reject),
  );

_put.withAsyncState = function <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(path: string, body: B) {
  return useAsyncState<R | null>(put(path, body), null);
};

const _patch = <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(
  path: string,
  body: B,
) =>
  new Promise<R>((resolve, reject) =>
    API.patch({
      apiName,
      path,
      options: {
        body,
      },
    })
      .response.then(async (response) => await _resolve<R>(response, resolve))
      .catch(catchMiddleware)
      .catch(reject),
  );

_patch.withAsyncState = function <
  B extends RequestBody | undefined = undefined,
  R extends DocumentType | unknown | void = DocumentType,
>(path: string, body: B) {
  return useAsyncState<R | null>(patch(path, body), null);
};

export const _delete = <R extends DocumentType | unknown | void = void>(
  path: string,
) =>
  new Promise<R>((resolve, reject) =>
    API.del({
      apiName,
      path,
    })
      .response.then(() => resolve(undefined as R))
      .catch(catchMiddleware)
      .catch(reject),
  );

_delete.withAsyncState = function <
  R extends DocumentType | unknown | void = void,
>(path: string) {
  return useAsyncState<R | null>(remove(path), null);
};

export const get = _get;
export const post = _post;
export const put = _put;
export const patch = _patch;
export const remove = _delete;
