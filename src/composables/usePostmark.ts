// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { queueMessage } from 'src/services/mailing/postmark';
import { Task, isClientMessageTask } from 'src/modules/tasks';

export const usePostmark = () => {
  const getTemplateAliasFromTask = (task: Task) =>
    isClientMessageTask(task)
      ? task.type
          .match(/([A-Z][a-z]+)/g)
          ?.slice(0, -1)
          .join('-')
          .toLowerCase()
      : undefined;

  return {
    getTemplateAliasFromTask,
    queueMessage,
  };
};
