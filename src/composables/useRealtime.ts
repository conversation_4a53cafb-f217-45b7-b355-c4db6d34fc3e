import Ably from 'ably';
import Push from 'ably/push';
import { tryOnMounted, tryOnUnmounted } from '@vueuse/core';

import { arrayUnique, arrayWrap } from 'src/utils/array/array';
import {
  Route,
  default as useRouteComposable,
} from 'src/composables/useRoute';
import { requestAuthToken } from 'src/modules/realtime';
//

const DEBUG = true;

// types
type TRealtimeOptions = Ably.ClientOptions;
type TRealtimeBroadcaster = Ably.Realtime;
type TChannel = Ably.RealtimeChannel;

type TEventType = string | (() => string | string[]);
type TEventCallback = (message: RealtimeMessage) => void;
type TChannelOptions = {
  listenerLookupKey?: string | number | null;
};
type TRealtimeScopeKey = 'clients' | 'advisers' | 'admins';

// internals
const { setRoute, getRoute, useRoute } = useRouteComposable();

const channelOptions: TChannelOptions = {
  listenerLookupKey: null,
};

const debug = (...params: any[]) => {
  if (DEBUG) {
    console.log(
      `%c[REALTIME] %c${params[0]} %c${params[1]}`,
      'color: #86c0d1; font-weight: bold; font-size: 12px;',
      'color: #a2bf8a; font-size: 12px;',
      'color: #d8dee9; font-size: 12px; font-style: italic;',
    );
  }
};

const resolveChannel = (
  channelName: string | number,
  options: TRealtimeOptions = {},
): RealtimeChannel => {
  return new RealtimeManager(options).resolveChannel(channelName);
};

const realTimeEventMap = {
  TASK_UPDATED: 'task:updated',
};

// exports
export class RealtimeMessage {
  timestamp?: number;
  data: any[];
  event: string;

  private constructor(private message: Ably.Message) {
    this.timestamp = message.timestamp;
    this.data = message.data?.data ?? message.data ?? [];
    this.event = message.name as string;
  }

  public static async build(message: Ably.Message, options: TChannelOptions) {
    return new RealtimeMessage(message);
  }

  public getData<T>(key: string | number): T | null {
    return this.data[key] ?? null;
  }

  public matches(event: TEventType | TEventType[]): boolean {
    return RealtimeChannel.normaliseEvents(event).includes(this.event);
  }
}

class RealtimeManager {
  private static instance: RealtimeManager | null = null;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  private broadcaster: TRealtimeBroadcaster;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  private channels: Map<string, RealtimeChannel>;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  private $route: Route;

  constructor(options: TRealtimeOptions = {}) {
    if (!RealtimeManager.instance) {
      this.$route = getRoute();
      this.init(options);
    }

    return RealtimeManager.instance as RealtimeManager;
  }

  private static normaliseChannelName(channelName: string | number): string {
    return 'private:' + String(channelName).toLowerCase().trim();
  }

  public resolveChannel(channelName: string | number): RealtimeChannel {
    channelName = RealtimeManager.normaliseChannelName(channelName);

    if (!this.hasChannel(channelName)) {
      this.storeChannel(new RealtimeChannel(this, channelName));
    }

    const channel = this.getChannel(channelName);

    channel.incrementUsing();

    return channel;
  }

  public getBroadcaster(): TRealtimeBroadcaster {
    return this.broadcaster;
  }

  public removeChannel(realTimeChannel: RealtimeChannel) {
    if (this.hasChannel(realTimeChannel.name)) {
      this.channels.delete(realTimeChannel.name);
    }
  }

  public debug(...params: any[]) {
    debug(...params);
  }

  public socketId(): string {
    return this.broadcaster.connection.key as string;
  }

  private getChannel(name: string): RealtimeChannel {
    return this.channels.get(name) as RealtimeChannel;
  }

  private storeChannel(channel: RealtimeChannel): void {
    this.channels.set(channel.name, channel);
  }

  private hasChannel(name: string): boolean {
    return this.channels.has(name);
  }

  private init(options: TRealtimeOptions = {}) {
    this.broadcaster = new Ably.Realtime({
      // defaults
      autoConnect: true,
      clientId: '<EMAIL>',
      // overrides
      ...options,
      echoMessages: false,
      idempotentRestPublishing: true,
      plugins: { Push },
      authCallback: async (tokenParams, callback) => {
        try {
          callback(null, await requestAuthToken());
        } catch (error) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          callback(error, null);
        }
      },
      recover: (lastConnectionDetails, callback) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        if (lastConnectionDetails.location.pathname === this.$route.fullPath) {
          callback(true);
        } else {
          callback(false);
        }
      },
    });

    this.channels = new Map();

    this.broadcaster.connection.on('connected', () => {
      this.debug('Realtime connection', 'connected');
    });
    this.broadcaster.connection.on('disconnected', () => {
      this.debug('Realtime connection', 'disconnected');
    });
    this.broadcaster.connection.on('suspended', () => {
      this.debug('Realtime connection', 'suspended');
      this.broadcaster.connection.close();
      RealtimeManager.instance = null;
    });

    RealtimeManager.instance = this;
  }
}

export class RealtimeChannel {
  public readonly name: string;
  private readonly channel: TChannel;
  public readonly broadcaster: TRealtimeBroadcaster;
  private using: number = 0;
  private listenerLookup: Map<
    string | number,
    { events: string[]; callback: (args?: any) => any }
  >;

  constructor(
    private manager: RealtimeManager,
    channelName: string,
  ) {
    this.broadcaster = manager.getBroadcaster();
    this.name = channelName;
    this.channel = this.broadcaster.channels.get(channelName);
    this.listenerLookup = new Map();

    this.channel.on('attached', () => {
      this.manager.debug(`Channel ${this.channel.name}`, 'attached');
    });
    this.channel.on('detached', () => {
      this.manager.debug(`Channel ${this.channel.name}`, 'detached');
      this.broadcaster.channels.release(this.channel.name);
    });
  }

  public static normaliseEvents(events: TEventType | TEventType[]): string[] {
    return arrayUnique(
      arrayWrap<TEventType>(events).flatMap<string>((event: TEventType) => {
        if (typeof event === 'function') {
          return RealtimeChannel.normaliseEvents(event());
        }

        return event;
      }),
    );
  }

  public on(
    event: TEventType | TEventType[],
    callback: (message: RealtimeMessage, options?: TChannelOptions) => void,
    options: TChannelOptions = {},
  ): this {
    const events = RealtimeChannel.normaliseEvents(event);
    options = {
      ...channelOptions,
      ...options,
    };

    const wrappedCallback = (payload: Ably.Message) =>
      this.fire(payload, callback, options);

    if (options.listenerLookupKey) {
      this.manager.debug(
        'Listening for',
        `${events.join()} (${options.listenerLookupKey})`,
      );
      this.listenerLookup.set(options.listenerLookupKey, {
        events,
        callback: wrappedCallback,
      });
    } else {
      this.manager.debug('Listening for', events.join());
    }

    this.channel.subscribe(events, wrappedCallback);

    return this;
  }

  public off(): this;
  public off(listenerLookup: string | number): this;
  public off(event?: TEventType | TEventType[]): this;
  public off(event?: any): this {
    if (typeof event === 'undefined') {
      this.manager.debug(`Stopped listening on ${this.name} for`, 'all events');
      this.channel.unsubscribe();
      return this;
    }

    if (this.listenerLookup.has(event)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      const { events, callback } = this.listenerLookup.get(event);
      this.manager.debug(
        `Stopped listening on ${this.name} for`,
        `${events.join() || 'all events'} (${event})`,
      );
      this.channel.unsubscribe(events, callback);
    } else {
      const events = RealtimeChannel.normaliseEvents(event);
      this.manager.debug(
        `Stopped listening on ${this.name} for`,
        events.join() || 'all events',
      );
      this.channel.unsubscribe(events);
    }

    return this;
  }

  public leave() {
    this.decrementUsing();
    if (this.using === 0) {
      this.off();
      this.channel.detach().then(() => {
        this.manager.removeChannel(this);
      });
    }
  }

  public decrementUsing(): void {
    this.using -= 1;
  }

  public incrementUsing(): void {
    this.using += 1;
  }

  /**
   * Subscribe the channel to push notifications on this channel.
   */
  public async subscribeChannelPush() {
    await this.channel.push
      .subscribeClient()
      .then((data) => {
        this.manager.debug('Subscribed to push on channel', this.channel.name);
      })
      .catch((error) => {
        this.manager.debug('Error subscribing to push on channel', error);
      });
  }

  public async unsubscribeChannelPush() {
    await this.channel.push
      .unsubscribeClient()
      .then((data) => {
        this.manager.debug(
          'Unsubscribed from push on channel',
          this.channel.name,
        );
      })
      .catch((error) => {
        this.manager.debug('Error unsubscribing from push on channel', error);
      });
  }

  private fire(
    payload: Ably.Message,
    callback: (message: RealtimeMessage, options?: TChannelOptions) => void,
    options: TChannelOptions,
  ) {
    this.manager.debug('Fired', payload.name);
    RealtimeMessage.build(payload, options).then((message: RealtimeMessage) => {
      callback(message, options);
    });
  }
}

/**
 * Use example:
 *
 * """
 * import { useRealtime } from 'src/composables/useRealtime';
 *
 * const { makePrivateChannel, eventMap } = useRealtime();
 * makePrivateChannel("clients", user.id)
 *  .on(eventMap.CASE_COMPLETED, () => {
 *    router.push("/cases");
 *  })
 *   .on(eventMap.TASK_UPDATED, ({ data }) => {
 *    router.push(`/cases/${data.caseId}#task-${data.taskSlug}`);
 *  })
 *  """
 */
export default function useRealTime() {
  const channelMap = new Map<string | number, RealtimeChannel>();

  const addChannelToMap = (
    key: string | number,
    channel: RealtimeChannel,
  ): void => {
    channelMap.set(key, channel);
  };

  class ChannelFactory {
    private scopes: string[] = [];
    private userId: number | null = null;
    private key?: number | string;
    private name: string;

    constructor(name: TRealtimeScopeKey, id?: number) {
      this.name = `${name}${id != null ? `:${id}` : ``}`;
    }

    /**
     * Used for referencing the channel in the channel map. When not supplied a
     * nano ID will be created and used.
     * @param key
     */
    public keyBy(key: number | string) {
      this.key = key;
      return this;
    }

    public scope(scopeBase: TRealtimeScopeKey, scopeId?: number): this {
      this.scopes.push(`${scopeBase}${scopeId != null ? `:${scopeId}` : ``}`);
      return this;
    }

    public scopeByUserId(userId: number) {
      this.userId = userId;
      return this;
    }

    on(
      event: TEventType | TEventType[],
      callback: TEventCallback,
      channelOptions: TChannelOptions = {},
      realtimeOptions: TRealtimeOptions = {},
    ): RealtimeChannel {
      const channel = this.resolve(realtimeOptions);

      if (!this.key) {
        this.key = crypto.randomUUID();
      }

      addChannelToMap(this.key, channel);

      return channel.on(event, callback, channelOptions);
    }

    private resolve(options: TRealtimeOptions = {}): RealtimeChannel {
      this.scopes.forEach((scope: string) => {
        this.name += `:${scope}`;
      });

      if (this.userId) {
        this.name += `:${this.userId}`;
      }

      return resolveChannel(this.name, options);
    }
  }

  const leaveAndClearChannels = (): void => {
    channelMap.forEach((channel: RealtimeChannel) => channel.leave());
    channelMap.clear();
  };
  const hasPrivateChannel = (key: string | number): boolean =>
    channelMap.has(key);
  const clearPrivateChannel = (key: string | number): void => {
    if (!hasPrivateChannel(key)) {
      return;
    }
    const channel = channelMap.get(key);
    if (channel) {
      channel.leave();
    }
    channelMap.delete(key);
  };

  const makePrivateChannel = (name: TRealtimeScopeKey, id?: number) => {
    return new ChannelFactory(name, id);
  };

  tryOnUnmounted(() => {
    leaveAndClearChannels();
  });

  tryOnMounted(() => {
    setRoute(useRoute());
  });

  return {
    eventMap: realTimeEventMap,
    leaveAndClearChannels,
    clearPrivateChannel,
    makePrivateChannel,
    hasPrivateChannel,
  };
}
