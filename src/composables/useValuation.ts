import { UnwrapRef, computed, ref, unref } from 'vue';
import { ListElement } from 'src/utils/list';
import { formatWithCurrency } from 'src/utils/money';
import { Valuation } from 'src/modules/factfind/models';

export function useValuationProcess<
  TItem extends ListElement<{ valuation: Valuation | null }>,
>({
  onAddNew,
  onAddToExisting,
}: {
  onAddNew: (item: UnwrapRef<TItem>) => void;
  onAddToExisting: (item: UnwrapRef<TItem>) => void;
}) {
  const processedItem = ref<TItem | null>(null);
  const action = ref<'AddNew' | 'AddToExisting' | 'Idle'>('Idle');
  const lastValuationInformation = computed<string>(() => {
    const lastValuation = computed(() => {
      if (!processedItem.value) return null;
      const existingValuation = processedItem.value.valuation;
      const pendingValuations = processedItem.value.pendingValuations || [];

      if (pendingValuations.length === 0) return existingValuation;

      const mostRecentPendingValuation = pendingValuations.reduce(
        (latest, current) => {
          return latest.date > current.date ? latest : current;
        },
      );

      if (!existingValuation) return mostRecentPendingValuation;

      return existingValuation.date > mostRecentPendingValuation.date
        ? existingValuation
        : mostRecentPendingValuation;
    });
    return lastValuation.value
      ? `Your latest valuation is ${formatWithCurrency(
          lastValuation.value.amount,
        )}, ${lastValuation.value.date.formatToView()}`
      : '';
  });

  async function addingValuationToNewElement(item: UnwrapRef<TItem>) {
    processedItem.value = unref(item);
    action.value = 'AddNew';
  }

  async function addingValuationToExistingItem(item: UnwrapRef<TItem>) {
    processedItem.value = item;
    action.value = 'AddToExisting';
  }

  async function addValuation(valuation: Valuation | null) {
    if (!processedItem.value) return;

    if (valuation) {
      if (!processedItem.value.pendingValuations) {
        processedItem.value.pendingValuations = [];
      }
      processedItem.value.pendingValuations.push(valuation);

      if (
        !processedItem.value.valuation ||
        valuation.date.valueOf() > processedItem.value.valuation.date.valueOf()
      ) {
        processedItem.value.valuation = valuation;
      }
    }

    if (action.value === 'AddNew') {
      onAddNew(processedItem.value);
    } else if (action.value === 'AddToExisting') {
      onAddToExisting(processedItem.value);
    }
    reset();
  }

  function reset() {
    action.value = 'Idle';
    processedItem.value = null;
  }

  return {
    addingValuationToNewElement,
    addingValuationToExistingItem,
    addValuation,
    cancel: async function () {
      if (processedItem.value && action.value === 'AddNew') {
        onAddNew(processedItem.value);
      }
      reset();
    },
    lastValuation: lastValuationInformation,
  };
}
