import type { Component } from 'vue';
import {
  PluginOptions,
  globalEventBus,
  useToast as useToastification,
} from 'vue-toastification';
import { AnyFunction } from 'src/types/Common';
import { parseErrorFromResponse } from 'src/services/api';
import { default as ActionableSuccessToast } from 'src/components/ActionableSuccessToast.vue';

export const toastPluginOptions: Partial<PluginOptions> = {
  timeout: 10000,
  pauseOnHover: false,
  bodyClassName: '',
};

interface ToastComponent {
  /**
   * Component that will be rendered.
   */
  component: Component;
  /**
   * `propName: propValue` pairs of props that will be passed to the component.
   *
   * __These are not reactive__
   */
  props?: { [propName: string]: unknown };
  /**
   * `eventName: eventHandler` pairs of events that the component can emit.
   */
  listeners?: { [listenerEvent: string]: AnyFunction };
}

type ToastContent = string | ToastComponent;

export type ToastOptions = Pick<
  PluginOptions,
  | 'closeButton'
  | 'icon'
  | 'hideProgressBar'
  | 'toastClassName'
  | 'timeout'
  | 'closeOnClick'
  | 'pauseOnHover'
  | 'position'
>;

const successToastClass = 'toast-success';
const errorToastClass = 'toast-error';
const warningToastClass = 'toast-warning';
const infoToastClass = 'toast-info';

export const useToast = () => {
  const toast = useToastification(globalEventBus);

  const warning = (content: string, options?: ToastOptions) => {
    toast.warning(content, {
      ...options,
      toastClassName: warningToastClass,
    });
  };

  const info = (content: string, options?: ToastOptions) => {
    toast.info(content, {
      ...options,
      toastClassName: infoToastClass,
    });
  };

  const success = (content: ToastContent, options?: ToastOptions) => {
    toast.success(content, {
      ...options,
      toastClassName: successToastClass,
    });
  };

  interface IActionableToast {
    'confirm-toast': AnyFunction;
    props: ToastComponent['props'];
  }

  const successWithAction = (
    params: IActionableToast,
    options?: Omit<ToastOptions, 'icon' | 'closeButton' | 'closeOnClick'>,
  ) => {
    const { props, ...listeners } = params;
    success(
      {
        component: ActionableSuccessToast,
        props,
        listeners,
      },
      {
        ...options,
        icon: false,
        closeButton: false,
        closeOnClick: false,
      },
    );
  };

  const error = (
    e: string | Error,
    defaultMessage?: string,
    options?: ToastOptions,
  ) => {
    if (typeof e === 'string') {
      return toast.error(e, {
        ...options,
        toastClassName: errorToastClass,
      });
    }

    // The defaultMessage will be if the error message cannot be parsed from the Error instance
    // and the default message returned by the server  (e.g.'Network Error' or 'Internal Server Error' in case of 5XX)
    // is not good enough.
    toast.error(parseErrorFromResponse(e, defaultMessage).join('\r\n'), {
      ...options,
      toastClassName: errorToastClass,
    });
  };

  return { info, success, successWithAction, error, warning };
};
