import { Goal } from 'src/types/store/Refdata';

export const objectivesExamples: Record<Goal['id'], string[]> = {
  1: [
    'I need an emergency fund in place.',
    'I want my emergency fund to be easily accessible in case of urgent need.',
    'If I use my emergency fund, I want a clear plan to replenish it promptly.',
  ],
  2: ['I want to ensure that I always have the best products for my needs.'],
  3: [
    'I want to ensure I am protected if I’m unable to work.',
    'I want to ensure my family is protected if something happens to me.',
  ],
  5: [
    'I want to retire at age ___.',
    'I estimate needing approximately £ ___ per month to live comfortably in retirement.',
    'I want to ensure I am contributing enough to my pension to meet my retirement goals.',
    'I have already retired and want to ensure I stay on track.',
  ],
  6: [
    'I want to ensure that upon my death everything is easy and setup correctly for my loved ones.',
    'I want to minimize the inheritance tax on my estate wherever possible.',
  ],
  7: ["I wish to have a robust investment plan for my children's future."],
  8: [],
  9: ['I want to plan my travels for the future to ensure I make them happen'],
  10: ['I need to build a lump sum for ___ in the future.'],
  11: [],
};
