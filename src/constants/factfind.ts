import { SelectOption } from 'src/components/form/fields/field-model';

export enum YesNoEnum {
  Yes = 'Yes',
  No = 'No',
}

export enum YesNoNeedsUpdatingEnum {
  Yes = 'Yes',
  NeedsUpdating = 'Needs updating',
  No = 'No',
}

export enum PreviousInvestmentExperienceEnum {
  No = 'No Experience',
  Small = 'Small',
  Average = 'Average',
  High = 'High',
}

export enum creditHistoryEnum {
  Good = 'Good',
  Medium = 'Medium',
  Bad = 'Bad',
}

export enum employmentStatusEnum {
  Employed = 'Employed',
  SelfEmployed = 'Self Employed',
  Unemployed = 'Unemployed',
  CompanyDirector = 'Company Director',
  Retired = 'Retired',
}

export const yesNoSelectOptions: SelectOption[] = [
  { label: YesNoEnum.Yes, value: true },
  { label: YesNoEnum.No, value: false },
];

export const yesNoNeedsUpdatingSelectOptions: SelectOption[] = [
  { label: YesNoNeedsUpdatingEnum.Yes, value: YesNoNeedsUpdatingEnum.Yes },
  {
    label: YesNoNeedsUpdatingEnum.NeedsUpdating,
    value: YesNoNeedsUpdatingEnum.NeedsUpdating,
  },
  { label: YesNoNeedsUpdatingEnum.No, value: YesNoNeedsUpdatingEnum.No },
];

export const previousInvestmentExperienceSelectOptions: SelectOption[] = [
  {
    label: PreviousInvestmentExperienceEnum.No,
    value: PreviousInvestmentExperienceEnum.No,
  },
  {
    label: PreviousInvestmentExperienceEnum.Small,
    value: PreviousInvestmentExperienceEnum.Small,
  },
  {
    label: PreviousInvestmentExperienceEnum.Average,
    value: PreviousInvestmentExperienceEnum.Average,
  },
  {
    label: PreviousInvestmentExperienceEnum.High,
    value: PreviousInvestmentExperienceEnum.High,
  },
];

export const creditHistorySelectOptions: SelectOption[] = [
  { label: creditHistoryEnum.Good, value: creditHistoryEnum.Good },
  { label: creditHistoryEnum.Medium, value: creditHistoryEnum.Medium },
  { label: creditHistoryEnum.Bad, value: creditHistoryEnum.Bad },
];

export const employmentStatusSelectOptions: SelectOption[] = [
  {
    label: employmentStatusEnum.Employed,
    value: employmentStatusEnum.Employed,
  },
  {
    label: employmentStatusEnum.SelfEmployed,
    value: employmentStatusEnum.SelfEmployed,
  },
  {
    label: employmentStatusEnum.Unemployed,
    value: employmentStatusEnum.Unemployed,
  },
  {
    label: employmentStatusEnum.CompanyDirector,
    value: employmentStatusEnum.CompanyDirector,
  },
  {
    label: employmentStatusEnum.Retired,
    value: employmentStatusEnum.Retired,
  },
];
