import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from 'src/modules/users';
import { useRefData } from 'src/stores/refdataStore';

export default () => {
  const { isReady } = storeToRefs(useRefData());
  const { userId } = storeToRefs(useUserStore());

  const isUserDataReady = computed<boolean>((): boolean => {
    return userId.value != null && isReady.value;
  });

  return {
    isUserDataReady,
  };
};
