import { array, number, object, string } from 'yup';
import { DateTime } from 'src/utils/dateTime';
import { QueryParams, apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import { AccountList } from '../models';

const getAccountsSchema = object({
  accounts: array()
    .of(
      object({
        id: number().required(),
        provider: string().required(),
        type: string().required(),
        clients: array()
          .of(
            object({
              id: number().required(),
              first_name: string().required(),
              last_name: string().defined(),
            }),
          )
          .required(),
        acc_no: string().min(0).defined(),
        sub_acc_no: string().min(0).defined(),
        created: string().required(),
        status: object({
          id: number().required(),
          name: string().required(),
        }).required(),
      }),
    )
    .required(),
  total_account_count: number().required(),
});

const fetchAccounts = async (
  queryParams?: QueryParams,
): Promise<AccountList> => {
  const response = await apiClient.get<Promise<unknown>>(
    '/api/v1/holdings/',
    queryParams,
  );

  const accountsDTO = await getAccountsSchema.validate(response);

  const accounts = accountsDTO.accounts.map((accountDTO) => ({
    id: accountDTO.id,
    providerName: accountDTO.provider,
    type: accountDTO.type,
    clients: accountDTO.clients.map((client) => ({
      id: client.id as ClientId,
      firstName: client.first_name,
      lastName: client.last_name,
    })),
    accountNumber: accountDTO.acc_no ?? '',
    subAccountNumber: accountDTO.sub_acc_no ?? '',
    created: new DateTime(accountDTO.created),
    status: accountDTO.status,
  }));
  return {
    items: accounts,
    totalItems: accountsDTO.total_account_count,
  };
};

export default fetchAccounts;
