import { apiClient } from 'src/services/api';
import { AllFormValues } from 'src/modules/accounts/models/form-model';
import { percentageDTOtoStr } from 'src/modules/accounts/models/account-plan-information';
import { AccountId } from '../types';

function getConstituents(constituents) {
  return constituents.map((lineDTO) => ({
    id: lineDTO.id,
    value: lineDTO.value,
    fund: lineDTO.fund_name,
    fundCharge: percentageDTOtoStr(lineDTO.fund_charge),
    ISIN: lineDTO.isin,
    investedPercentage: percentageDTOtoStr(lineDTO.invested_percent),
    weightedCharge: percentageDTOtoStr(lineDTO.weighted_charge),
  }));
}

function investmentMappings(plan) {
  return {
    id: plan.id,
    productLayout: plan.data_layout,
    dateOfInformation: plan.date_of_information,
    notes: plan.notes,
    portfolioModel: plan.portfolio_model_id,
    fundValue: plan.data.fund_value?.toString() || null,
    transferValue: plan.data.transfer_value?.toString() || null,
    chargesFigures: {
      chargesPlanAMC: percentageDTOtoStr(
        plan.data.charges_figures.charges_plan_amc,
      ),
      chargesFundAMC: percentageDTOtoStr(
        plan.data.charges_figures.charges_fund_amc,
      ),
      chargesDFMfee: percentageDTOtoStr(
        plan.data.charges_figures.charges_dfm_fee,
      ),
      additionalfeePAAmount:
        plan.data.charges_figures?.any_additional_pounds_fee_pa?.toString() ||
        null,
      annualOverallChargePAPercentageFigures: percentageDTOtoStr(
        plan.data.charges_figures.annual_overall_charge_pa_percentage_figures,
      ),
    },
    otherInvestmentInfo: {
      withProfits: plan.data.other_investment_information.with_profits,
      contributingHistoryOnFile:
        plan.data.other_investment_information.contributing_history_on_file,
      availableFundsForSwitchOnFile:
        plan.data.other_investment_information
          .available_funds_for_switch_on_file,
      adviserRemunerationAllowable:
        plan.data.other_investment_information.adviser_remuneration_allowable,
      withdrawalScheduleOnFile:
        plan.data.other_investment_information.withdrawal_schedule_on_file,
      transactionCGTReportOnFile:
        plan.data.other_investment_information.transaction_cgt_report_on_file,
      wrapperStructureDetailsOnFile:
        plan.data.other_investment_information
          .wrapper_structure_details_on_file,
      segmentationStructureHistoryOnFile:
        plan.data.other_investment_information
          .segmentation_structure_history_on_file,
    },

    constituents: getConstituents(plan.constituents),
  };
}

function pensionMappings(plan) {
  return {
    id: plan.id,
    productLayout: plan.data_layout,
    dateOfInformation: plan.date_of_information,
    portfolioModel: plan.portfolio_model_id,
    fundValue: plan.data.fund_value?.toString() || null,
    transferValue: plan.data.transfer_value?.toString() || null,
    notes: plan.notes,
    chargesFigures: {
      chargesPlanAMC: percentageDTOtoStr(
        plan.data.charges_figures.charges_plan_amc,
      ),
      chargesFundAMC: percentageDTOtoStr(
        plan.data.charges_figures.charges_fund_amc,
      ),
      chargesDFMfee: percentageDTOtoStr(
        plan.data.charges_figures.charges_dfm_fee,
      ),
      additionalfeePAAmount:
        plan.data.charges_figures?.any_additional_pounds_fee_pa?.toString() ||
        null,
      annualOverallChargePAPercentageFigures: percentageDTOtoStr(
        plan.data.charges_figures.annual_overall_charge_pa_percentage_figures,
      ),
    },
    chargesProjections: {
      commencementDate: plan.data.charges_projections.account_commencement_date,
      retirementDate: plan.data.charges_projections.retirement_date,
      dateOfProjection: plan.data.charges_projections.date_of_projection,
      retirementDateOnProjection:
        plan.data.charges_projections.retirement_date_on_projections,
      rateOfProjection: percentageDTOtoStr(
        plan.data.charges_projections.rate_of_projection,
      ),
      projectionFigure:
        plan.data.charges_projections?.projection_figure_pounds?.toString() ||
        null,
      annualOverallChargePAPercentageProjections: percentageDTOtoStr(
        plan.data.charges_projections.projections_overall_charge_pa,
      ),
    },
    safeguardingBenefits: {
      guaranteedAnnuityRateGAR:
        plan.data.safeguarding_benefits.guaranteed_annuity_rate_gar,
      guaranteedMinPensionGMP:
        plan.data.safeguarding_benefits.guaranteed_min_pension_gmp,
      isTFCGreaterThan25Percentage:
        plan.data.safeguarding_benefits.is_tfc_greater_than_25_percent,
      anyLoyaltyBonus: plan.data.safeguarding_benefits.any_loyalty_bonus,
      protectedRetirementAge:
        plan.data.safeguarding_benefits.protected_retirement_age,
    },
    otherPensionInfo: {
      UFPLSAvailable: plan.data.other_pension_information.ufpls_available,
      dischargeFormsOnFile:
        plan.data.other_pension_information.discharge_forms_on_file,
      drawdownAvailable: plan.data.other_pension_information.drawdown_available,
      nomineeFlexiAccessDrawdownAvailable:
        plan.data.other_pension_information
          .nominee_flexi_access_drawdown_available,
      withProfits: plan.data.other_pension_information.with_profits,
      switchingStrategyOnFile:
        plan.data.other_pension_information.switching_strategy_on_file,
      lifeCover: plan.data.other_pension_information.life_cover,
      contributingHistoryOnFile:
        plan.data.other_pension_information.contributing_history_on_file,
      deathBenefitsOnFile:
        plan.data.other_pension_information.death_benefits_on_file,
      availableFundsForSwitchOnFile:
        plan.data.other_pension_information.available_funds_for_switch_on_file,
      adviserRemunerationAllowable:
        plan.data.other_pension_information.adviser_remuneration_allowable,
    },
    constituents: getConstituents(plan.constituents),
  };
}

function pension_income_paying_layout(plan) {
  return {
    id: plan.id,
    productLayout: plan.data_layout,
    dateOfInformation: plan.date_of_information,
    notes: plan.notes,
    otherPensionIncomePaying: {
      deathBenefitsOnFile:
        plan.data.other_pension_income_information.death_benefits_on_file,
      detailsOfSchemeOnFile:
        plan.data.other_pension_income_information.details_of_scheme_on_file,
      detailsOfPensionableServiceOnFile:
        plan.data.other_pension_income_information
          .details_of_pensionable_service_on_file,
    },
  };
}

function protectionMappings(plan) {
  return {
    id: plan.id,
    productLayout: plan.data_layout,
    dateOfInformation: plan.date_of_information,
    notes: plan.notes,
    otherProtectionInfo: {
      adviserRemunerationAllowable:
        plan.data.other_protection_information.adviser_remuneration_allowable,
      insuranceTypeDetailsOnFile:
        plan.data.other_protection_information.insurance_type_details_on_file,
      detailsOfTrustOnFile:
        plan.data.other_protection_information.details_of_trust_on_file,
      insuranceCostDetailsOnFile:
        plan.data.other_protection_information.insurance_cost_details_on_file,
      detailsOfPersonInsuredOnFile:
        plan.data.other_protection_information
          .details_of_person_insured_on_file,
      insuranceHistoryDetailsOnFile:
        plan.data.other_protection_information
          .insurance_history_details_on_file,
      latestPolicyStatementOnFile:
        plan.data.other_protection_information.latest_policy_statement_on_file,
    },
  };
}

const mappings_setup = {
  investment_layout: investmentMappings,
  pension_layout: pensionMappings,
  protection_layout: protectionMappings,
  pension_income_paying_layout: pension_income_paying_layout,
};

export default async (
  accountId: AccountId,
  dataLayout: string,
): Promise<Array<AllFormValues>> => {
  const response = await apiClient.get<Promise<Array<any>>>(
    `/api/v2/holdings/${accountId}/plan_information`,
  );

  return response.map((plan) => {
    return mappings_setup[dataLayout](plan);
  });
};
