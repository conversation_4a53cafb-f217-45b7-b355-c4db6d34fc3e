import { apiClient } from 'src/services/api';
import { AccountId } from 'src/modules/accounts';

interface Body {
  is_actual: boolean;
  amount: number;
  date: string;
}

interface Response {
  type: string;
  amount: number;
  date: string;
}

export default async (accountId: AccountId, body: Body) => {
  await apiClient.post<Body, Promise<Response>>(
    `/api/v1/holdings/${accountId}/valuation`,
    body,
  );
};
