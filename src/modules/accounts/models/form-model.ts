import { Nullable } from 'src/types/Common';
import { SingleAccount } from 'src/modules/accounts';

interface ChargesFigures {
  chargesPlanAMC: Nullable<string>;
  chargesFundAMC: Nullable<string>;
  chargesDFMfee: Nullable<string>;
  additionalfeePAAmount: Nullable<string>;
  annualOverallChargePAPercentageFigures: Nullable<string>;
}

interface ChargesProjections {
  commencementDate: Nullable<string>;
  retirementDate: Nullable<string>;
  dateOfProjection: Nullable<string>;
  retirementDateOnProjection: Nullable<string>;
  rateOfProjection: Nullable<string>;
  projectionFigure: Nullable<string>;
  annualOverallChargePAPercentageProjections: Nullable<string>;
}

interface SafeguardingBenefits {
  guaranteedAnnuityRateGAR: Nullable<boolean>;
  guaranteedMinPensionGMP: Nullable<boolean>;
  isTFCGreaterThan25Percentage: Nullable<boolean>;
  anyLoyaltyBonus: Nullable<boolean>;
  protectedRetirementAge: Nullable<boolean>;
}

interface OtherPensionDetails {
  UFPLSAvailable: Nullable<boolean>;
  drawdownAvailable: Nullable<boolean>;
  nomineeFlexiAccessDrawdownAvailable: Nullable<boolean>;
  withProfits: Nullable<boolean>;
  dischargeFormsOnFile: Nullable<boolean>;
  switchingStrategyOnFile: Nullable<boolean>;
  lifeCover: Nullable<boolean>;
  contributingHistoryOnFile: Nullable<boolean>;
  deathBenefitsOnFile: Nullable<boolean>;
  availableFundsForSwitchOnFile: Nullable<boolean>;
  adviserRemunerationAllowable: Nullable<boolean>;
}

interface OtherInvestmentInfo {
  withProfits: Nullable<boolean>;
  contributingHistoryOnFile: Nullable<boolean>;
  availableFundsForSwitchOnFile: Nullable<boolean>;
  adviserRemunerationAllowable: Nullable<boolean>;
  withdrawalScheduleOnFile: Nullable<boolean>;
  transactionCGTReportOnFile: Nullable<boolean>;
  wrapperStructureDetailsOnFile: Nullable<boolean>;
  segmentationStructureHistoryOnFile: Nullable<boolean>;
}

interface OtherProtectionInfo {
  adviserRemunerationAllowable: Nullable<boolean>;
  insuranceTypeDetailsOnFile: Nullable<boolean>;
  detailsOfTrustOnFile: Nullable<boolean>;
  insuranceCostDetailsOnFile: Nullable<boolean>;
  detailsOfPersonInsuredOnFile: Nullable<boolean>;
  insuranceHistoryDetailsOnFile: Nullable<boolean>;
  latestPolicyStatementOnFile: Nullable<boolean>;
}

interface OtherPensionIncomePayingInfo {
  deathBenefitsOnFile: Nullable<boolean>;
  detailsOfSchemeOnFile: Nullable<boolean>;
  detailsOfPensionableServiceOnFile: Nullable<boolean>;
}

interface ConstituentValue {
  id: number | null;
  ISIN: string;
  fund: string;
  value: number;
  fundCharge: number;
  investedPercentage: number;
  weightedCharge: number;
}

interface FormValues {
  id?: number | string;
  dateOfInformation: string;
  productLayout: string;
  notes: Nullable<string>;
}

export interface PensionFormValues extends FormValues {
  fundValue: Nullable<string>;
  transferValue: Nullable<string>;
  portfolioModel: Nullable<number>;
  // Charges fields
  chargesFigures: ChargesFigures;
  chargesProjections: ChargesProjections;
  // Other details
  safeguardingBenefits: SafeguardingBenefits;
  otherPensionInfo: OtherPensionDetails;
  constituents: Array<ConstituentValue>;
}

export interface InvestmentFormValues extends FormValues {
  fundValue: Nullable<string>;
  transferValue: Nullable<string>;
  portfolioModel: Nullable<number>;
  // Charges fields
  chargesFigures: ChargesFigures;
  // Other details

  otherInvestmentInfo: OtherInvestmentInfo;
  constituents: Array<ConstituentValue>;
}

export interface ProtectionFormValues extends FormValues {
  otherProtectionInfo: OtherProtectionInfo;
}

export interface PensionIncomePayingFormValues extends FormValues {
  otherPensionIncomePaying: OtherPensionIncomePayingInfo;
}

const initialValuesPension = {
  id: 'NEW',
  dateOfInformation: '',
  productLayout: 'pension_layout',
  fundValue: null,
  transferValue: null,
  portfolioModel: null,
  chargesFigures: {
    chargesPlanAMC: null,
    chargesFundAMC: null,
    chargesDFMfee: null,
    additionalfeePAAmount: null,
    annualOverallChargePAPercentageFigures: null,
  },
  chargesProjections: {
    commencementDate: null,
    retirementDate: null,
    dateOfProjection: null,
    retirementDateOnProjection: null,
    rateOfProjection: null,
    projectionFigure: null,
    annualOverallChargePAPercentageProjections: null,
  },
  safeguardingBenefits: {
    guaranteedAnnuityRateGAR: null,
    guaranteedMinPensionGMP: null,
    isTFCGreaterThan25Percentage: null,
    anyLoyaltyBonus: null,
    protectedRetirementAge: null,
  },
  otherPensionInfo: {
    UFPLSAvailable: null,
    drawdownAvailable: null,
    nomineeFlexiAccessDrawdownAvailable: null,
    withProfits: null,
    dischargeFormsOnFile: null,
    switchingStrategyOnFile: null,
    lifeCover: null,
    contributingHistoryOnFile: null,
    deathBenefitsOnFile: null,
    availableFundsForSwitchOnFile: null,
    adviserRemunerationAllowable: null,
  },
  constituents: [],
  notes: null,
};

const initialValuesInvestment = {
  id: 'NEW',
  dateOfInformation: '',
  productLayout: 'investment_layout',
  fundValue: null,
  transferValue: null,
  portfolioModel: null,
  chargesFigures: {
    chargesPlanAMC: null,
    chargesFundAMC: null,
    chargesDFMfee: null,
    additionalfeePAAmount: null,
    annualOverallChargePAPercentageFigures: null,
  },
  otherInformation: {
    UFPLSAvailable: null,
    drawdownAvailable: null,
    nomineeFlexiAccessDrawdownAvailable: null,
    withProfits: null,
    dischargeFormsOnFile: null,
    switchingStrategyOnFile: null,
    lifeCover: null,
    contributingHistoryOnFile: null,
    deathBenefitsOnFile: null,
    availableFundsForSwitchOnFile: null,
    adviserRemunerationAllowable: null,
  },
  constituents: [],
  notes: null,
};

const initialValuesProtection = {
  id: 'NEW',
  dateOfInformation: '',
  productLayout: 'protection_layout',
  notes: null,
  protectionInfo: {
    adviserRemunerationAllowable: null,
    insuranceTypeDetailsOnFile: null,
    detailsOfTrustOnFile: null,
    insuranceCostDetailsOnFile: null,
    detailsOfPersonInsuredOnFile: null,
    insuranceHistoryDetailsOnFile: null,
    latestPolicyStatementOnFile: null,
  },
};

const initialValuesPensionIncomePaying = {
  id: 'NEW',
  dateOfInformation: '',
  productLayout: 'pension_income_paying_layout',
  notes: null,
  pensionIncomePayingInfo: {
    adviserRemunerationAllowable: null,
    insuranceTypeDetailsOnFile: null,
    detailsOfTrustOnFile: null,
    insuranceCostDetailsOnFile: null,
    detailsOfPersonInsuredOnFile: null,
    insuranceHistoryDetailsOnFile: null,
    latestPolicyStatementOnFile: null,
  },
};

const initialValues = {
  pension_layout: initialValuesPension,
  investment_layout: initialValuesInvestment,
  protection_layout: initialValuesProtection,
  pension_income_paying_layout: initialValuesPensionIncomePaying,
};

export type AllFormValues =
  | PensionFormValues
  | InvestmentFormValues
  | ProtectionFormValues
  | PensionIncomePayingFormValues;

export const isInvestment = (
  value: AllFormValues,
): value is InvestmentFormValues => {
  return value.productLayout === 'investment_layout';
};

export const isPension = (value: AllFormValues): value is PensionFormValues => {
  return value.productLayout === 'pension_layout';
};

export const isProtection = (
  value: AllFormValues,
): value is ProtectionFormValues => {
  return value.productLayout === 'protection_layout';
};

export const isPensionIncomePaying = (
  value: AllFormValues,
): value is PensionIncomePayingFormValues => {
  return value.productLayout === 'pension_income_paying_layout';
};

export const getInitialValues = (
  productLayout: string,
  account: SingleAccount,
): AllFormValues => {
  const values = initialValues[productLayout];
  if (account.portfolioModelId) {
    values['portfolioModel'] = account.portfolioModelId;
  }
  return values;
};
