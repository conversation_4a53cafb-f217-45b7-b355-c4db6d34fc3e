import { find, get } from 'lodash';
import { useRefData } from 'src/stores';
import { Nullable } from 'src/types/Common';
import { IDateTime } from 'src/utils/dateTime';
import { Paginable } from 'src/types/Pagination';
import { Advisor } from 'src/modules/advisors';
import { Client } from 'src/modules/clients/models';
import {
  AccountId,
  Advice,
  type ExpectedFee,
} from 'src/modules/accounts';
import { ClientTypeEnum } from 'src/modules/clients/models/client';
import { FeeSplitTemplate } from 'src/modules/refdata/fee-split-template';

export interface AccountStatus {
  id: number;
  name: string;
}

export interface AccountModelFactor {
  id: AccountId;
  providerName: string;
  type: ClientTypeEnum | string;
  typeId: number;
  typeGroupId: number;
  clients: Pick<Client, 'id' | 'lastName' | 'firstName'>[];
  accountNumber: Nullable<string>;
  subAccountNumber: Nullable<string>;
  created: IDateTime;
  originalStatus: AccountStatus;
  status: AccountStatus;
  advices: Advice[];
  advisor: Pick<Advisor, 'id' | 'firstName' | 'lastName'>;
  feeSplitTemplate: Nullable<number>;
  expectedFees: ExpectedFee[];
}

export type AccountListItem = Pick<
  AccountModelFactor,
  | 'id'
  | 'providerName'
  | 'type'
  | 'clients'
  | 'accountNumber'
  | 'subAccountNumber'
  | 'created'
  | 'status'
>;

export interface AccountList extends Paginable<AccountListItem> {}

export type Account = Pick<
  AccountModelFactor,
  | 'id'
  | 'providerName'
  | 'typeId'
  | 'feeSplitTemplate'
  | 'clients'
  | 'accountNumber'
  | 'subAccountNumber'
  | 'created'
  | 'status'
  | 'type'
>;

export type AccountProviderId = number;
export type SingleAccount = Pick<
  AccountModelFactor,
  | 'id'
  | 'typeId'
  | 'clients'
  | 'accountNumber'
  | 'subAccountNumber'
  | 'created'
  | 'status'
> & {
  providerId: Nullable<AccountProviderId>;
  advisor: Nullable<AccountModelFactor['advisor']>;
  feeSplitTemplate: Nullable<FeeSplitTemplate>;
  additionalInfo: string;
  hasPlanInformation: boolean;
  hasInvestmentMix: boolean;
  feeModel: Nullable<number>;
  portfolioModelId: Nullable<number>;
  productLayout: Nullable<string>;
};

export enum AccountStatusEnum {
  NotSet = 0,
  Proposed = 1,
  ActiveAdvised = 2,
  ActiveNonAdvised = 3,
  Closed = 4,
  ClosedCancelled = 5,
  ClosedTransferred = 6,
  AccountKept = 7,
}

export const AccountActiveStatuses = [
  AccountStatusEnum.NotSet,
  AccountStatusEnum.ActiveAdvised,
  AccountStatusEnum.ActiveNonAdvised,
  AccountStatusEnum.AccountKept,
];

export const isProposedAccount = (status: AccountStatus) => {
  return status.id === AccountStatusEnum.Proposed;
};

export const isAccountToReview = (status: AccountStatus) => {
  const { getHoldingsReviewableInACase } = useRefData();
  return getHoldingsReviewableInACase.map(({ id }) => id).includes(status.id);
};

export const accountStatusToSelectOption = (status: AccountStatusEnum) => ({
  id: status,
  name: AccountStatusEnum[status],
});

export const getStatusName = (
  statusId?: number | undefined,
): string | undefined => {
  const { getHoldingStatuses } = useRefData();
  return get(find(getHoldingStatuses, { id: statusId }), 'name');
};

export enum EstimatedRiskLevelEnum {
  DontKnow = -1,
  AV0 = 0,
  AV1 = 1,
  AV2 = 2,
  AV3 = 3,
  AV4 = 4,
  AV5 = 5,
}

export type EstimatedRiskLevelType =
  (typeof EstimatedRiskLevelEnum)[keyof typeof EstimatedRiskLevelEnum];

export enum PaymentDirectionEnum {
  Contribution = 'contribution',
  Withdrawal = 'withdrawal',
}

export type PaymentDirectionType =
  (typeof PaymentDirectionEnum)[keyof typeof PaymentDirectionEnum];
