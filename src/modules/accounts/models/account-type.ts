import { holdingTypes } from 'src/modules/refdata/refdata';

export interface IAccountType {
  toString(): string;
  toNumber(): number;
}

export class AccountType implements IAccountType {
  constructor(private typeId: number) {}

  toString(): string {
    return (
      holdingTypes.find((holding) => holding.id === this.typeId)?.name || ''
    );
  }

  toNumber(): number {
    return this.typeId;
  }
}
