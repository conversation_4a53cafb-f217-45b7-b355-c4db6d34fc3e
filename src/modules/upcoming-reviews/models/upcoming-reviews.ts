import { Mailable } from 'src/types/Mailable';
import { CaseId } from 'src/modules/cases';
import { ClientId } from 'src/modules/clients';
import { IReviewType } from 'src/modules/cases/types/review-types';

interface ReviewClient extends Mailable {
  id: ClientId;
  firstName: string;
  lastName: string;
}

interface ReviewCase {
  id: CaseId;
  adviserId: number;
  status: number;
}

export interface UpcomingReview {
  id: number;
  clients: ReviewClient[];
  clientOwnerId: number;
  reviewMonth: number;
  reviewYear: number;
  reviewType: IReviewType;
  caseData: ReviewCase | null;
}

export interface UpcomingReviewsResponse {
  totalReviews: number;
  upcomingReviews: UpcomingReview[];
}
