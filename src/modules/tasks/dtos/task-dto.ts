import { pascalCase } from 'change-case-all';
import { DateTime } from 'src/utils/dateTime';
import { AdvisorDTO, AdvisorRoleEnum } from 'src/modules/advisors';
import { TaskId } from 'src/modules/tasks';
import { GoalId } from 'src/modules/goals';
import { SubTask } from 'src/modules/cases/models';
import { Task } from '../models/task';
import { TaskStatus } from '../models/task-status';
import { TaskTypeEnum } from '../models/task-type';

const taskLayoutToType = (layout: string | null): TaskTypeEnum =>
  layout &&
  Object.values(TaskTypeEnum).includes(pascalCase(layout) as TaskTypeEnum)
    ? TaskTypeEnum[pascalCase(layout)]
    : TaskTypeEnum.Default;

export interface SubTaskDTO {
  task_id: number;
  goal_id: number | null;
  case_goal_id: number | null;
  no_documents_reason: string;
}

export interface TaskDTO {
  task_slug: string;
  description: string;
  process_description: string | null;
  case_level: boolean;
  screen_layout: string | null;
  default_group: AdvisorRoleEnum;
  assignee: AdvisorDTO | null;
  assignee_group: AdvisorRoleEnum | null;
  status: number;
  due_date: string | null;
  sub_tasks: SubTaskDTO[];
  is_modifiable: boolean;
}

const goalDtoToDomain = (dto: SubTaskDTO): SubTask => ({
  taskId: dto.task_id as TaskId,
  caseGoalId: dto.case_goal_id,
  goalId: dto.goal_id as GoalId,
  noDocumentsReason: dto.no_documents_reason,
});

export const dtoToDomain = (dto: TaskDTO): Task => ({
  slug: dto.task_slug,
  type: taskLayoutToType(dto.screen_layout),
  caseLevel: dto.case_level,
  description: dto.description,
  process_description: dto.process_description,
  defaultGroup: dto.default_group,
  status: new TaskStatus(dto.status),
  dueDate: new DateTime(dto.due_date),
  advisor: dto.assignee
    ? {
        firstName: dto.assignee.first_name,
        id: dto.assignee.id,
        lastName: dto.assignee.last_name,
      }
    : null,
  assignedGroup: dto.assignee_group || null,
  subTasks: dto.sub_tasks?.map(goalDtoToDomain),
  isModifiable: dto.is_modifiable,
});
