import { SelectOption } from 'src/components/form/fields/field-model';

export enum TaskStatusEnum {
  ToDo = 1,
  InProgress = 2,
  Completed = 3,
  Canceled = 4,
  NotApplicable = 5,
  Review = 6,
}

export type TaskStatusType =
  | TaskStatusEnum.ToDo
  | TaskStatusEnum.InProgress
  | TaskStatusEnum.Completed
  | TaskStatusEnum.Canceled
  | TaskStatusEnum.NotApplicable
  | TaskStatusEnum.Review;

export const getStatusLabel = (taskStatus: TaskStatusType): string => {
  const map = new Map<TaskStatusType, string>([
    [TaskStatusEnum.ToDo, 'To do'],
    [TaskStatusEnum.InProgress, 'In Progress'],
    [TaskStatusEnum.Completed, 'Completed'],
    [TaskStatusEnum.Canceled, 'Canceled'],
    [TaskStatusEnum.NotApplicable, 'N/A'],
    [TaskStatusEnum.Review, 'Review'],
  ]);

  return map.get(taskStatus) as string;
};

export interface ITaskStatus {
  toString(): string;
  toType(): TaskStatusType;
}

export class TaskStatus implements ITaskStatus {
  constructor(private type: TaskStatusType) {}

  toString() {
    return getStatusLabel(this.type);
  }

  toType() {
    return this.type;
  }
}

export const taskStatusOptions: SelectOption[] = Object.values(TaskStatusEnum)
  .filter((value) => !isNaN(Number(value)) && value !== TaskStatusEnum.Review)
  .map<SelectOption>((value: string | TaskStatusEnum) => ({
    label: getStatusLabel(value as TaskStatusEnum),
    value,
  }));
