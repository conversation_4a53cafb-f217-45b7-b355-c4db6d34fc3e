import { array, number, object, string } from 'yup';
import { apiClient } from 'src/services/api';
import { QueryParams } from 'src/types/api';
import { DateTime } from 'src/utils/dateTime';
import { CaseType } from 'src/modules/cases/models';
import { TaskList } from '../models/tasks';
import { TaskStatus } from '../models/task-status';

const getTasksValidationSchema = object({
  data: array()
    .of(
      object({
        id: number().required(),
        task_slug: string().required(),
        description: string().required(),
        case_id: number().required(),
        case_type: number().required(),
        client_id: number().required(),
        case_name: string().required(),
        goal_name: string().nullable().defined(),
        goal_id: number().nullable().defined(),
        status: number().required(),
        assignee: object({
          id: number().required().defined(),
          first_name: string().required().defined(),
          last_name: string().defined(),
        }).nullable(),
        due_date: string().nullable().defined(),
        assignee_group: string().nullable().defined(),
      }),
    )
    .required(),
  count: number().required(),
});

export default async (queryParams?: QueryParams): Promise<TaskList> => {
  const response = await apiClient.get<Promise<unknown>>(
    '/api/v1/task/',
    queryParams,
  );

  const tasksDTO = await getTasksValidationSchema.validate(response);

  const tasks = tasksDTO.data.map((taskDTO) => ({
    id: taskDTO.id,
    slug: taskDTO.task_slug,
    description: taskDTO.description,
    caseId: taskDTO.case_id,
    clientId: taskDTO.client_id,
    caseName: taskDTO.case_name,
    goalName: taskDTO.goal_name,
    goalId: taskDTO.goal_id,
    status: new TaskStatus(taskDTO.status),
    assignee: taskDTO.assignee
      ? {
          id: taskDTO.assignee.id,
          firstName: taskDTO.assignee.first_name,
          lastName: taskDTO.assignee.last_name,
        }
      : null,
    dueDate: new DateTime(taskDTO.due_date),
    caseType: new CaseType(taskDTO.case_type),
    assigneeGroup: taskDTO.assignee_group,
  }));
  return {
    tasks,
    totalTasks: tasksDTO.count,
  };
};
