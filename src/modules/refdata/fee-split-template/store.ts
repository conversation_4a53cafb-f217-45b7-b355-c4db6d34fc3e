import { defineStore } from 'pinia';
import { DateTime, IDateTime } from 'src/utils/dateTime';
import { FeeSplitTemplate } from './fee-split-template';
import fetchFeeSplitTemplatesForAdvisor from './fetch-fee-split-templates-for-advisor';
import { Advisor } from 'src/modules/advisors';
import { Nullable } from 'src/types/Common';

export interface State {
  items: FeeSplitTemplate[];
  activeOnlyItems: FeeSplitTemplate[];
  fetchedTime: Nullable<IDateTime>;
  lastAdvisorUsed: Nullable<Advisor['id']>;
  status: {
    error: Error | null;
    pending: boolean;
  };
}

interface Actions {
  fetchAdvisorItems: (advisor: Advisor['id']) => Promise<void>;
}

type Getters = {
  list: () => FeeSplitTemplate[];
  activeOnly: () => FeeSplitTemplate[];
};

export const useStore = defineStore<
  'fee_split_templates',
  State,
  Getters,
  Actions
>('fee_split_templates', {
  state: () => ({
    items: [],
    activeOnlyItems: [],
    fetchedTime: null,
    lastAdvisorUsed: null,
    status: {
      error: null,
      pending: false,
    },
  }),
  actions: {
    async fetchAdvisorItems(advisorId) {
      try {
        this.status.pending = true;

        if (this.lastAdvisorUsed !== advisorId) {
          const fetchedItems =
            await fetchFeeSplitTemplatesForAdvisor(advisorId);
          this.items = fetchedItems;
          this.activeOnlyItems = fetchedItems.filter((item) => item.isActive);
          this.fetchedTime = new DateTime(new Date().toString());
          this.status.pending = false;
        }

        this.lastAdvisorUsed = advisorId;
      } catch (e: unknown) {
        if (e instanceof Error) {
          this.status.error = e;
        } else {
          this.status.error = new Error(
            'Something went wrong while fetching fee split templates.',
          );
        }
      }
    },
  },

  getters: {
    list(): FeeSplitTemplate[] {
      return this.items;
    },
    activeOnly(): FeeSplitTemplate[] {
      return this.activeOnlyItems;
    },
  },
});
