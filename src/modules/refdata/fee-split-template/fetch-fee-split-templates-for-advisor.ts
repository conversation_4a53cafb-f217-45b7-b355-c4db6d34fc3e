import { apiClient } from 'src/services/api';
import { FeeSplitTemplate } from 'src/modules/refdata/fee-split-template';
import { array, boolean, number, object, string } from 'yup';

const getFeeSplitTemplatesValidationSchema = object({
  fee_split_templates: array()
    .of(
      object({
        id: number().required(),
        template_name: string().required(),
        is_active: boolean().required(),
      }),
    )
    .required(),
});

export default async (
  advisorId: number,
  isActive = true,
): Promise<FeeSplitTemplate[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/fee-split-templates`,
    { administrator_id: advisorId, is_active: isActive },
  );

  const feeSplitTemplates =
    await getFeeSplitTemplatesValidationSchema.validate(response);

  return feeSplitTemplates.fee_split_templates.map((template) => ({
    id: template.id,
    name: template.template_name,
    isActive: template.is_active,
  }));
};
