import { InferType, array, mixed, number, object, string } from 'yup';
import {
  Product,
  ProductType,
} from 'src/modules/refdata/types/Product';
import { get } from 'src/services/api/apiClient';

const getProductsSchema = array()
  .of(
    object({
      id: number().required(),
      name: string().required(),
      product_type_group_id: number().required(),
      type: mixed<ProductType>()
        .oneOf([
          'account',
          'property',
          'company_shares',
          'crypto_currency',
          'other_asset',
          'mortgage',
          'personal_loan',
          'credit_card',
          'other_debt',
        ])
        .required(),
    }).required(),
  )
  .required();

const mapDtoToProduct = (
  dto: InferType<typeof getProductsSchema>[0],
): Product => {
  return {
    ...dto,
    productTypeGroupId: dto.product_type_group_id,
  };
};

export async function fetchAccountLikeProducts(): Promise<Product[]> {
  try {
    const response = await get<unknown>('/api/v1/refdata/product_types', {
      product_variant: 'account_like',
    });
    const productsDto = await getProductsSchema.validate(response);

    return (productsDto || []).map(mapDtoToProduct);
  } catch (e) {
    return [];
  }
}
