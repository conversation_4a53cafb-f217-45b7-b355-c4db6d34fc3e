import { Nullable } from 'src/types/Common';

enum FlowTypeEnum {
  INCOME = 'Income',
  EXPENDITURE = 'Expenditure',
}

type CashFlowBase = {
  id: number;
  group_name: string;
  income_expenditure_group_id: number;
  name: string;
  is_essential: Nullable<boolean>;
};

export type Income = CashFlowBase & {
  flow_type: FlowTypeEnum.INCOME;
};

export type Expenditure = CashFlowBase & {
  flow_type: FlowTypeEnum.EXPENDITURE;
};

export const isIncomeFlow = (flow: any): flow is Income => {
  return flow.flow_type === FlowTypeEnum.INCOME;
};

export const isExpenditureFlow = (flow: any): flow is Expenditure => {
  return flow.flow_type === FlowTypeEnum.EXPENDITURE;
};

export type CashFlow = Income | Expenditure;
