import { onMounted } from 'vue';
import { storeToRefs } from 'pinia';

import { SelectOption } from 'src/components/form/fields/field-model';

import { useCashflowStore } from './cashflow-store';
import { Expenditure, Income } from './cashflow';
//

interface UseCashflow {
  getIncomeGroupsOptions: () => SelectOption[];
  getExpenditureGroupsOptions: () => SelectOption[];
  getExpenditureGroupTypesOptions: (groupId: number) => SelectOption[];
  getIncomeGroupTypesOptions: (groupId: number) => SelectOption[];
  getIncomeByTypeId: (typeId: number) => Income | undefined;
  getExpenditureByTypeId: (id: number) => Expenditure | undefined;
}

export const useCashFlow = (): UseCashflow => {
  const { setCashflows } = useCashflowStore();
  const {
    incomeGroups,
    expenditureGroups,
    getIncomesByGroupId,
    getExpendituresByGroupId,
    incomeByTypeId,
    expenditureByTypeId,
  } = storeToRefs(useCashflowStore());

  onMounted(async () => {
    await setCashflows();
  });

  const getIncomeGroupsOptions = (): SelectOption[] => {
    return incomeGroups.value.map((income) => ({
      value: income.income_expenditure_group_id,
      label: income.group_name,
    }));
  };

  const getExpenditureGroupsOptions = () => {
    return expenditureGroups.value.map((expenditure) => ({
      value: expenditure.income_expenditure_group_id,
      label: expenditure.group_name,
    }));
  };

  const getIncomeGroupTypesOptions = (groupId: number) => {
    return getIncomesByGroupId.value(groupId).map((income) => ({
      value: income.id,
      label: income.name,
    }));
  };

  const getExpenditureGroupTypesOptions = (groupId: number) => {
    return getExpendituresByGroupId.value(groupId).map((expenditure) => ({
      value: expenditure.id,
      label: expenditure.name,
      ctx: { is_essential: expenditure.is_essential },
    }));
  };

  const getIncomeByTypeId = (typeId: number): Income | undefined => {
    return incomeByTypeId.value(typeId);
  };

  const getExpenditureByTypeId = (typeId: number): Expenditure | undefined => {
    return expenditureByTypeId.value(typeId);
  };

  return {
    getIncomeGroupsOptions,
    getExpenditureGroupsOptions,
    getExpenditureGroupTypesOptions,
    getIncomeGroupTypesOptions,
    getIncomeByTypeId,
    getExpenditureByTypeId,
  };
};
