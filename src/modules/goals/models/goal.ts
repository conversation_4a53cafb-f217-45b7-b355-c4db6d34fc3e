import { CaseClient } from 'src/modules/cases';
import { ClientId } from 'src/modules/clients';
import { GoalAccount } from 'src/modules/goals/models/goal-account';
import { ClientGoalTypeEnum } from 'src/modules/clients/models';
import { GoalId } from '../types';

export interface ModelFactor {
  id: GoalId;
  name: string;
  type: ClientGoalTypeEnum;
  clients: CaseClient[];
}

export type GoalObjectives = {
  clientId: ClientId;
  content: string;
};

// CaseGoal
export interface Goal extends ModelFactor {
  accounts: GoalAccount[];
  goalObjectives: GoalObjectives[];
}
