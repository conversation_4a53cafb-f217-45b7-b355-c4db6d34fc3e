import { AccountModelFactor } from 'src/modules/accounts';
import { Nullable } from 'src/types/Common';

export interface GoalAccount
  extends Pick<
    AccountModelFactor,
    | 'id'
    | 'providerName'
    | 'type'
    | 'typeGroupId'
    | 'accountNumber'
    | 'subAccountNumber'
    | 'advices'
    | 'status'
    | 'originalStatus'
    | 'feeSplitTemplate'
    | 'expectedFees'
    | 'clients'
  > {
  advisorId: Nullable<AccountModelFactor['advisor']['id']>;
}
