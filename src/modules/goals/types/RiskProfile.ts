import { EventBusKey } from '@vueuse/core';
import { Nullable } from 'src/types/Common';
import { RiskLevelType } from 'src/modules/goals';

export type TRiskQuestion = {
  question: string;
  answers: Array<{ answer: string; description?: string }>;
};

export type RiskAnswersType = { [k: string]: number };

export type RiskProfileData = {
  recommended_risk: RiskLevelType;
  risk_attitude: number;
  loss_tolerance: number;
  updated_at?: Nullable<string>;
  answers: RiskAnswersType;
};

export const profilerEventKey: EventBusKey<{ name: ProfilerEventType }> =
  Symbol('profiler');

export const ProfilerEvent = {
  SUBMIT: 'profile:submit',
  RESET: 'profile:reset',
} as const;

export type ProfilerEventType =
  (typeof ProfilerEvent)[keyof typeof ProfilerEvent];
