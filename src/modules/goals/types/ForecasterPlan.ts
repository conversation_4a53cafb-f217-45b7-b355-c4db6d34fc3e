import { Dictionary } from 'lodash';
import { InjectionKey, Ref } from 'vue';
import { EventBusKey } from '@vueuse/core';

import { Nullable } from 'src/types/Common';
import { GrowthRate, RiskLevel } from 'src/constants';
//

export const recommendedRiskInjectionKey = Symbol() as InjectionKey<
  Ref<RiskLevelType | undefined>
>;
export const selectedRiskInjectionKey = Symbol() as InjectionKey<
  Ref<RiskLevelType | undefined>
>;

export const buildingPlanInjectionKey = Symbol() as InjectionKey<Ref<boolean>>;

export type RiskLevelType = (typeof RiskLevel)[keyof typeof RiskLevel];
export type GrowthRateType = (typeof GrowthRate)[keyof typeof GrowthRate];
export type RiskProfileType = {
  [p in RiskLevelType]: [GrowthRateType, [number, number]];
};
export const RiskProfile: RiskProfileType = {
  [RiskLevel.HIGH]: [GrowthRate['HIGH'], [-18.54, 0.0]],
  [RiskLevel.MODERATE]: [GrowthRate['MODERATE'], [-13.78, 0.0]],
  [RiskLevel.CONSERVATIVE]: [GrowthRate['CONSERVATIVE'], [-9.58, 0.0]],
  [RiskLevel.DEFENSIVE]: [GrowthRate['DEFENSIVE'], [-9.81, 0.0]],
  [RiskLevel.LOW]: [GrowthRate['LOW'], [-11.17, 0.0]],
};

export enum ContributionType {
  MonthlyDeposit = 'MonthlyDeposit',
  QuarterlyDeposit = 'QuarterlyDeposit',
  AnnualDeposit = 'AnnualDeposit',
  LumpSumDeposit = 'LumpSumDeposit',
}
export enum WithdrawalType {
  MonthlyWithdrawal = 'MonthlyWithdrawal',
  QuarterlyWithdrawal = 'QuarterlyWithdrawal',
  AnnualWithdrawal = 'AnnualWithdrawal',
  LumpSumWithdrawal = 'LumpSumWithdrawal',
  FullWithdrawal = 'FullWithdrawal',
}
export type CashflowType<T extends WithdrawalType | ContributionType> = (
  value: T,
) => T extends WithdrawalType ? `${WithdrawalType}` : `${ContributionType}`;
export interface CashflowItem {
  type: ContributionType | WithdrawalType;
  start: string | number;
  end: string | number | null;
  amount?: number | null;
}

export type ContributionItem = { type: ContributionType } & CashflowItem;
export type WithdrawalItem = { type: WithdrawalType } & CashflowItem;
export type CashflowItemsList<T extends ContributionItem | WithdrawalItem> =
  Dictionary<T[]>;

type RiskLevelKeyType =
  | 'RiskLevel1'
  | 'RiskLevel2'
  | 'RiskLevel3'
  | 'RiskLevel4'
  | 'RiskLevel5';
type GrowthBalanceKeyType =
  | 'GrowthBalance1'
  | 'GrowthBalance2'
  | 'GrowthBalance3'
  | 'GrowthBalance4'
  | 'GrowthBalance5';
type PartialRecord<K extends string | number | symbol, T> = { [P in K]?: T };
type RiskLevel = PartialRecord<RiskLevelKeyType, number>;
type GrowthBalance = PartialRecord<GrowthBalanceKeyType, number>;
type ForecasterResultItem = {
  Age: number;
  In?: number;
  Out?: number;
  Inflation?: number;
  Interest?: number;
  Points?: number;
  CashBalance: number;
} & GrowthBalance &
  RiskLevel;

type ExtrasLossCapacity = Record<'abs_value' | 'pct_value', number>;
type ForecasterExtras = { loss_capacity: ExtrasLossCapacity };

export interface ForecasterResults {
  data: { [key: number | string]: ForecasterResultItem };
  extras: ForecasterExtras;
}

export interface ForecasterResultsDTO {
  data: ForecasterResults['data'];
  extras: Nullable<ExtrasLossCapacity>;
}

export const getCashflowItemTitle = (
  _t: ContributionType | WithdrawalType,
): string => {
  switch (_t) {
    case ContributionType.MonthlyDeposit:
      return 'Monthly contribution';
    case WithdrawalType.MonthlyWithdrawal:
      return 'Monthly withdrawal';
    case ContributionType.QuarterlyDeposit:
      return 'Quarterly contribution';
    case WithdrawalType.QuarterlyWithdrawal:
      return 'Quarterly withdrawal';
    case ContributionType.AnnualDeposit:
      return 'Annual contribution';
    case WithdrawalType.AnnualWithdrawal:
      return 'Annual withdrawal';
    case ContributionType.LumpSumDeposit:
      return 'One-off contribution';
    case WithdrawalType.LumpSumWithdrawal:
      return 'One-off withdrawal';
    case WithdrawalType.FullWithdrawal:
      return 'Full withdrawal';
    default:
      return '';
  }
};
export const getCashflowItemCaption = (
  _t: ContributionType | WithdrawalType,
): string => {
  switch (_t) {
    case ContributionType.MonthlyDeposit:
    case WithdrawalType.MonthlyWithdrawal:
      return 'per month';
    case ContributionType.QuarterlyDeposit:
    case WithdrawalType.QuarterlyWithdrawal:
      return 'per quarter';
    case ContributionType.AnnualDeposit:
    case WithdrawalType.AnnualWithdrawal:
      return 'per annum';
    case ContributionType.LumpSumDeposit:
    case WithdrawalType.LumpSumWithdrawal:
      return 'one-off';
    case WithdrawalType.FullWithdrawal:
    default:
      return 'full withdrawal';
  }
};

export const forecasterEventKey: EventBusKey<{ name: ForecasterEventType }> =
  Symbol('forecaster');

export const ForecasterEvent = {
  EXPORT: 'forecaster:export',
  SUBMIT: 'forecaster:submit',
  RESET: 'forecaster:reset',
} as const;

export type ForecasterEventType =
  (typeof ForecasterEvent)[keyof typeof ForecasterEvent];

export type ForecasterStatsValue = string | number | null | undefined;
export type ForecasterStatsType = {
  name: string;
  value: ForecasterStatsValue;
}[];

export interface ForecasterOptions {
  retirementAge: Nullable<number>;
  selectedRisk: Nullable<RiskLevelType>;
  inflationAdjusted: boolean;
  inflationRate: Nullable<number>;
}

export interface ForecasterData {
  cashflows: CashflowItem[];
  options: Partial<ForecasterOptions>;
  comments?: Nullable<string>;
  updated_at?: Nullable<string>;
}
