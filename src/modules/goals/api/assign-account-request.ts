import { apiClient } from 'src/services/api';
import { CaseId, TaskCustomCommand } from 'src/modules/cases';
import { TaskId } from 'src/modules/tasks';

type Command = TaskCustomCommand.AddAccountsToReview;

type Body = {
  account_ids: number[];
};

export default async (
  caseId: CaseId,
  taskId: TaskId,
  accountIds: number[],
): Promise<void> => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/accounts_to_review/${taskId}`,
    {
      command: TaskCustomCommand.AddAccountsToReview,
      payload: { account_ids: accountIds },
    },
  );
};
