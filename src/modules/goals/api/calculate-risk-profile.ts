import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import {
  GoalId,
  RiskAnswersType,
  RiskProfileData,
} from 'src/modules/goals';
import {
  RiskProfileRequestDTO,
  RiskProfileResponseDTO,
} from 'src/modules/goals/dtos/risk-profile-dto';

type PropType<T, K extends keyof T> = T[K];
const mapAnswers = (answers: number[]): RiskAnswersType =>
  Object.fromEntries(answers.map((j, i) => ['a' + i, j]));

export const calculateRiskProfile = async (
  goalId: GoalId,
  clientId: ClientId,
  answers: Array<PropType<RiskAnswersType, 'key'>>,
): Promise<RiskProfileData> => {
  const data = await apiClient.post<
    RiskProfileRequestDTO,
    RiskProfileResponseDTO
  >(`/api/v1/goals/${goalId}/risk-profile`, {
    client_id: clientId,
    answers,
  });

  return {
    recommended_risk: data.recommended_risk,
    risk_attitude: data.risk_attitude,
    loss_tolerance: data.loss_tolerance,
    updated_at: data.updated_at,
    answers: mapAnswers(data.answers),
  };
};

export const fetchRiskProfile = async (
  goalId: GoalId,
  clientId: ClientId,
): Promise<RiskProfileData | null> => {
  const data = await apiClient.get<RiskProfileResponseDTO>(
    `/api/v1/goals/${goalId}/risk-profile`,
    {
      client_id: clientId,
    },
  );

  return data
    ? {
        recommended_risk: data.recommended_risk,
        risk_attitude: data.risk_attitude,
        loss_tolerance: data.loss_tolerance,
        updated_at: data.updated_at,
        answers: mapAnswers(data.answers),
      }
    : null;
};
