import { Nullable } from 'src/types/Common';
import { DateTime } from 'src/utils/dateTime';
import { ClientId } from 'src/modules/clients';
import { ExpectedFeeType } from 'src/modules/accounts/models/expected-fee-type';
import { CaseGoal } from '../models/case–goal';
import { AdviceFrequencyEnum } from 'src/modules/factfind/models/frequency';

export interface GoalAccountDto {
  holding_id: number;
  account_number: string;
  sub_account_number: string;
  provider: string;
  type: string;
  product_type_group_id: number;
  status: {
    id: number;
    name: string;
  };
  original_status: {
    id: number;
    name: string;
  };
  adviser_id: Nullable<number>;
  fee_split_template_id: Nullable<number>;
  clients: Array<{ id: number; first_name: string; last_name: string | null }>;
  advice: Array<{
    advice_note: string;
    advice_type_id: number;
    advice_group_id: Nullable<number>;
    id: number;
    is_advice_implemented: boolean;
    is_advice_accepted: boolean;
    amount: number | null;
    frequency: AdviceFrequencyEnum | null;
    portfolio_id: number | null;
    referenced_account_id: number | null;
  }>;
  expected_fees: Array<{
    id: number;
    fee_type: number;
    due_date: string;
    amount: number;
  }>;
}

export const apiToDomain = (dto: GoalAccountDto): CaseGoal['accounts'][0] => ({
  accountNumber: dto.account_number,
  id: dto.holding_id,
  providerName: dto.provider,
  subAccountNumber: dto.sub_account_number,
  status: dto.status,
  originalStatus: dto.original_status,
  type: dto.type,
  typeGroupId: dto.product_type_group_id,
  advisorId: dto.adviser_id,
  feeSplitTemplate: dto.fee_split_template_id,
  clients: dto.clients.map((client) => ({
    id: client.id as ClientId,
    firstName: client.first_name,
    lastName: client.last_name,
  })),
  advices: dto.advice.map((adviceDto) => ({
    id: adviceDto.id,
    description: adviceDto.advice_note,
    type: adviceDto.advice_type_id,
    isImplemented: adviceDto.is_advice_implemented,
    isAccepted: adviceDto.is_advice_accepted,
    amount: adviceDto.amount !== null ? adviceDto.amount : undefined,
    frequency: adviceDto.frequency !== null ? adviceDto.frequency : undefined,
    portfolioId:
      adviceDto.portfolio_id !== null ? adviceDto.portfolio_id : undefined,
    accountId:
      adviceDto.referenced_account_id !== null
        ? adviceDto.referenced_account_id
        : undefined,
  })),
  expectedFees: dto.expected_fees.map((feeDto) => ({
    id: feeDto.id,
    initial: new ExpectedFeeType(feeDto.fee_type),
    dueDate: new DateTime(feeDto.due_date),
    amount: feeDto.amount.toString(),
  })),
});
