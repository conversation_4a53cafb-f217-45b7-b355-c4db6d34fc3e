import { apiClient } from 'src/services/api';
import { TaskDTO, taskDtoToDomain } from 'src/modules/tasks';
import {
  GoalAccountDto,
  apiToDomain as goalAccountDtoToDomain,
} from 'src/modules/cases/dtos/goal-account-dto';
import { Nullable } from 'src/types/Common';
import { GoalId } from 'src/modules/goals';
import { ClientId } from 'src/modules/clients';
import { CaseId } from '../types';
import { Case, CaseType } from '../models';
import { fromDTO } from './case-status-mapper';

interface GetCaseDTO {
  id: number;
  name: string;
  status: 1 | 2 | 3; // 1 -> Open, 2 -> Completed, 3 -> Cancelled
  case_type: 1 | 2; // 1 -> New Business, 2 -> Annual Review
  adviser: {
    id: number;
    first_name: string;
    last_name: string;
    role: string;
  };
  manager: {
    id: number;
    first_name: string;
    last_name: string;
    role: string;
  } | null;
  clients: Array<{
    id: number;
    first_name: string;
    last_name: string;
    email: Nullable<string>;
  }>;
  goals: Array<{
    id: number;
    goal_name: string;
    goal_id: number;
    account_advice: Array<GoalAccountDto>;
    clients: Array<{
      id: number;
      first_name: string;
      last_name: string;
      email: Nullable<string>;
    }>;
    goal_objectives: Array<{
      client_id: number;
      content: string;
    }>;
  }>;
  tasks: Array<TaskDTO>;
  created_on: string;
  completed_on: string;
  days_to_complete: number;
  review_slot: Nullable<{
    id: number;
    description: string;
  }>;
}

interface FetchedCases
  extends Pick<
    Case,
    | 'id'
    | 'status'
    | 'name'
    | 'createdOn'
    | 'completedOn'
    | 'daysToComplete'
    | 'relatedAdvisor'
    | 'relatedManager'
    | 'goals'
    | 'tasks'
    | 'type'
    | 'clients'
    | 'reviewSlot'
  > {}

export default async (caseId: CaseId): Promise<FetchedCases> => {
  const caseDTO = await apiClient.get<Promise<GetCaseDTO>>(
    `/api/v2/case/${caseId}`,
  );

  return {
    id: caseDTO.id as CaseId,
    name: caseDTO.name,
    status: fromDTO(caseDTO.status),
    createdOn: new Date(caseDTO.created_on),
    completedOn: caseDTO.completed_on ? new Date(caseDTO.completed_on) : null,
    daysToComplete: caseDTO.days_to_complete,
    type: new CaseType(caseDTO.case_type),
    relatedAdvisor: {
      id: caseDTO.adviser.id,
      firstName: caseDTO.adviser.first_name,
      lastName: caseDTO.adviser.last_name,
    },
    relatedManager: caseDTO.manager
      ? {
          id: caseDTO.manager.id,
          firstName: caseDTO.manager.first_name,
          lastName: caseDTO.manager.last_name,
        }
      : null,
    clients: caseDTO.clients.map((client) => ({
      id: client.id as ClientId,
      firstName: client.first_name,
      lastName: client.last_name,
      email: client.email,
    })),
    goals: caseDTO.goals.map((goalDTO) => ({
      id: goalDTO.id as GoalId,
      name: goalDTO.goal_name,
      type: goalDTO.goal_id,
      accounts: goalDTO.account_advice.map(goalAccountDtoToDomain),
      clients: goalDTO.clients.map((client) => ({
        id: client.id as ClientId,
        firstName: client.first_name,
        lastName: client.last_name,
        email: client.email,
      })),
      goalObjectives: goalDTO.goal_objectives.map((obj) => ({
        clientId: obj.client_id as ClientId,
        content: obj.content,
      })),
    })),
    tasks: caseDTO.tasks.map(taskDtoToDomain),
    reviewSlot: caseDTO.review_slot
      ? {
          id: caseDTO.review_slot.id,
          description: caseDTO.review_slot.description,
        }
      : null,
  };
};
