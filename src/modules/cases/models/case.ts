import { ArrayElement, Nullable } from 'src/types/Common';
import { Person } from 'src/types/Person';
import { Advisor } from 'src/modules/advisors';
import { ReviewSlot } from 'src/modules/upcoming-reviews';
import { CaseModelFactor } from 'src/modules/cases';
import { Task, isTaskFinished } from 'src/modules/tasks';
import { CaseId } from '../types';
import { ICaseType } from './case-type';
import { type CaseGoal } from './case–goal';
import { CaseProgress } from './case-progress';
import { CaseStatus, CaseStatusEnum } from './case-status';
import { CaseClient as Client } from './case-client';

export type ModelFactor = {
  id: CaseId;
  name: string;
  caseType: ICaseType;
  relatedAdvisor: Pick<Advisor, keyof Person>;
  relatedManager: Pick<Advisor, keyof Person> | null;
  progress: CaseProgress;
  clients: Client[];
  status: CaseStatus;
  createdOn: Date;
  completedOn: Nullable<Date>;
  daysToComplete: number;
  type: ICaseType; //TODO: Remove and update the test helpers and Storybook
  reviewSlot: Nullable<ReviewSlot>;
};

export interface Case
  extends Pick<
    ModelFactor,
    | 'id'
    | 'name'
    | 'status'
    | 'relatedAdvisor'
    | 'relatedManager'
    | 'createdOn'
    | 'completedOn'
    | 'daysToComplete'
    | 'type'
    | 'clients'
    | 'reviewSlot'
  > {
  goals: Array<CaseGoal>;
  tasks: Array<Task>;
}

export interface CaseClient extends ArrayElement<CaseModelFactor['clients']> {}

export const isCanceled = (status: Case['status']) =>
  status === CaseStatusEnum.Cancelled;

export const isCompleted = (status: Case['status']) =>
  status === CaseStatusEnum.Completed;

export const canBeClosed = (tasks: Case['tasks']) => {
  return tasks.every(isTaskFinished);
};
