import { Nullable, Options } from 'src/types/Common';

export interface ICaseType {
  toString: () => string;
  toNumber: () => number;
}

export enum CaseTypeEnum {
  NewBusiness = 1,
  AnnualReview = 2,
  Onboarding = 3,
  AdHocWithdrawal = 4,
  Catchup = 5,
  AdHocItem = 6,
}

export class CaseType implements ICaseType {
  constructor(private value: number) {}

  toString(): string {
    const map = new Map<number, string>([
      [CaseTypeEnum.NewBusiness, 'New Business'],
      [CaseTypeEnum.AnnualReview, 'Annual Review'],
      [CaseTypeEnum.Onboarding, 'Onboarding'],
      [CaseTypeEnum.AdHocWithdrawal, 'Ad-Hoc Withdrawal'],
      [CaseTypeEnum.Catchup, 'Catch-up'],
      [CaseTypeEnum.AdHocItem, 'Ad-Hoc To-Do Item'],
    ]);

    return map.get(this.value) ?? '';
  }

  toNumber(): number {
    return this.value;
  }
}

export const caseTypesOptions: Readonly<Options<number>[]> = [
  { id: 1, name: 'New Business' },
  { id: 2, name: 'Annual Review' },
  { id: 3, name: 'Onboarding' },
  { id: 4, name: 'Ad-Hoc Withdrawal' },
  { id: 5, name: 'Catch-up' },
  { id: 6, name: 'Ad-Hoc To-Do Item' },
];

export const isAnnualReviewCase = (caseType: Nullable<CaseTypeEnum>) =>
  caseType === CaseTypeEnum.AnnualReview;

export const isOnboardingCase = (caseType: Nullable<CaseTypeEnum>) =>
  caseType === CaseTypeEnum.Onboarding;
