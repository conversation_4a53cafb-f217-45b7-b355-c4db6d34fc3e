import { IDateTime } from 'src/utils/dateTime';
import { Advisor } from 'src/modules/advisors';
import { Client } from 'src/modules/clients';
import { CaseStatus } from './case-status';
import { CaseType } from 'src/modules/cases/models/case-type';

export interface Goal {
  id: number;
  goalId: number;
  goalName: string;
}
export interface CasesListItem {
  id: number;
  caseName: string;
  caseType: CaseType;
  status: CaseStatus;
  clients: Pick<Client, 'id' | 'firstName' | 'lastName'>[];
  adviser: Pick<Advisor, 'id' | 'firstName' | 'lastName'>;
  goals: Goal[];
  tasksCompleted: number;
  taskTotal: number;
  dueDate: IDateTime | null;
}

export interface CaseList {
  cases: CasesListItem[];
  totalCases: number;
}
