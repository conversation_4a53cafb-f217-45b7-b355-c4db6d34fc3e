import { Period } from 'src/modules/clients/types/Period';
import { SelectedRange } from 'src/modules/clients/types/SelectedRange';
import { DateTime } from 'src/utils/dateTime';
import { startOfYear, subMonths, subYears } from 'date-fns';

export const rangeToDtoPeriodMapper = (range: SelectedRange): Period => {
  const map = new Map<SelectedRange, Period>([
    ['1m', 'daily'],
    ['6m', 'daily'],
    ['1y', 'weekly'],
    ['3y', 'weekly'],
    ['5y', 'monthly'],
    ['All', 'monthly'],
  ]);

  return map.get(range) as Period;
};

export const rangeToDtoFromDateMapper = (range: SelectedRange): string => {
  const map = new Map<SelectedRange, string>([
    ['1m', new DateTime(subMonths(new Date(), 1)).formatForForm()],
    ['6m', new DateTime(subMonths(new Date(), 6)).formatForForm()],
    ['1y', new DateTime(subYears(new Date(), 1)).formatForForm()],
    ['3y', new DateTime(subYears(new Date(), 3)).formatForForm()],
    ['5y', new DateTime(subYears(new Date(), 5)).formatForForm()],
    [
      'All',
      new DateTime(startOfYear(subYears(new Date(), 50))).formatForForm(),
    ],
  ]);

  return map.get(range) as string;
};
