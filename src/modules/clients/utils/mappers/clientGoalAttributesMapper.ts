import {
  ClientGoal,
  ClientGoalTypeEnum,
} from 'src/modules/clients/models';
import { GoalAttrsDTO } from 'src/modules/factfind/dtos/goal.dto';
//

const _domainMapper = (attributes: GoalAttrsDTO) => ({
  targetAmount: attributes.target_amount ? attributes.target_amount : null,
  targetDate: attributes.target_date ? attributes.target_date : null,
});

const _dtoMapper = (
  goalType: ClientGoalTypeEnum,
  attrs: ClientGoal['attributes'],
) => ({
  type_: goalType,
  target_amount: attrs.targetAmount ?? null,
  target_date: attrs.targetDate ?? null,
});

export class ClientGoalAttrsMapper {
  static toDomain = (dto: GoalAttrsDTO): ClientGoal['attributes'] =>
    _domainMapper(dto);

  static toDTO = (
    goalType: ClientGoalTypeEnum,
    attrs: ClientGoal['attributes'],
  ): Required<GoalAttrsDTO> => _dtoMapper(goalType, attrs);
}
