import { Nullable } from 'src/types/Common';

export interface Holding {
  id: number;
  product: Nullable<Product>;
  accountNumber: string;
  subAccountNumber: string;
  clients: Array<{
    id: number;
    first_name: string;
    last_name: string;
    email: Nullable<string>;
  }>;
  provider: {
    id: number;
    name: string;
  };
  feeSplitTemplate: Nullable<number>;
}

export interface GoalLinkedHolding {
  id: number;
  accountNumber: string;
  providerName: string;
  productTypeName: string;
}

export interface Product {
  attributes: HoldingAttributes;
  current_valuation: ProductValuation;
  product_type: {
    name: string;
  };
}

export interface HoldingAttributes {
  account_number: string | null;
  sub_account_number: string | null;
}

export interface ProductValuation {
  value: number;
  valuation_date: Date | null;
}
