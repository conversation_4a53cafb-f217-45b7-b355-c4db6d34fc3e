import { Nullable } from 'src/types/Common';
import { ClientId } from 'src/modules/clients';
import {
  ForecasterData,
  GoalId,
  RiskProfileData,
} from 'src/modules/goals';

import { GoalLinkedHolding } from './holding';

export interface Goal {
  id: GoalId;
  goalTypeId: number;
  name: string;
  description: string;
  cases: number[];
  clientIds: ClientId[];
  objectives: string | null;
  linkedHoldings: GoalLinkedHolding[];
}

export enum ClientGoalTypeEnum {
  EmergencyFund = 1,
  PropertyOwnership = 2,
  ProtectYourselfAndFamily = 3,
  Retirement = 5,
  EstatePlanningAndWills = 6,
  InvestForChildren = 7,
  InvestForSchoolFees = 8,
  TravelPlanning = 9,
  BuildWealth = 10,
  Custom = 11,
  ClientSetup = 12,
  CaseTask = 13,
}

export type ClientGoalAttributes = {
  targetAmount: Nullable<number>;
  targetDate: Nullable<string>;
};

export type ClientGoal = Pick<
  Goal,
  | 'id'
  | 'goalTypeId'
  | 'name'
  | 'cases'
  | 'clientIds'
  | 'objectives'
  | 'linkedHoldings'
> & {
  riskProfile: RiskProfileData | null;
  cashForecast: ForecasterData | null;
  attributes: ClientGoalAttributes;
};

export interface GoalWithHealthScore extends ClientGoal {
  healthScore: {
    score: number;
    auditResults: Record<string, any>;
  };
  warnings: Record<string, any>;
  errors: string;
}
