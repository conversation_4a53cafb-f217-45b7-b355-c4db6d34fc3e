import { DeepNullable } from 'src/types/Common';
import { createAddress as createBaseAddress } from 'src/services/address';
import { ClientAddress } from 'src/modules/factfind/types/Address';
//

export default function createClientAddress(
  address: Partial<ClientAddress>,
): DeepNullable<ClientAddress> {
  return {
    ...createBaseAddress(address),
    isPrimary: address.isPrimary || null,
  };
}
