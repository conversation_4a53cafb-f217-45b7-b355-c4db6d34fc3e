import { Advisor } from 'src/modules/advisors';
import { ClientAddress } from 'src/modules/factfind/types/Address';
import { ClientId } from '../../types';
import { ClientType } from './client-type';
import { ClientLink } from './client-link';
import { ClientStatus, ClientStatuses } from './client-status';
import { ReviewFrequency, ReviewMonth } from '../review-frequency';

export interface Client {
  id: ClientId;
  type: string;
  email: string | null;
  noEmailReasonId: number | null;
  firstName: string;
  lastName: string | null;
  title: number;
  genderId: number | null;
  dateOfBirth: Date | null;
  nationalityId: number | null;
  birthCountryId: number | null;
  primaryCountryId: number | null;
  secondaryCountryId: number | null;
  maritalStatusId: number | null;
  phoneNumber: string | null;
  mobileNumber: string | null;
  addresses: ClientAddress[];
  linkedClients: ClientLink[];
  advisor: ClientAdvisor;
  clientType: ClientType;
  clientStatus: ClientStatus;
  clientSource: number;
  reviewFrequency: ReviewFrequency | null;
  reviewMonth: ReviewMonth | null;
  nextReviewMonth: ReviewMonth | null;
  marketingId: number | null;
  accessEnabled: boolean | null;
  clientAgreementId: number | null;
  privacyNoticeId: number | null;
}

export interface Clients
  extends Array<{
    id: ClientId;
    email: string | null;
    firstName: string;
    lastName: string | null;
    clientTypeId: number;
    clientStatusId: number;
    advisor: Pick<Advisor, 'id' | 'firstName' | 'lastName'> | null;
    accountsCount: number;
    linksCount: number;
    reviewFrequency: ReviewFrequency | null;
    reviewMonth: ReviewMonth | null;
  }> {}

export interface ClientList {
  clients: Clients;
  totalClientCount: number;
  statuses: ClientStatuses[];
}

export interface ClientFactfind {
  alreadyRetired: boolean | null;
  creditHistoryId: number | null;
  employmentStatusId: number | null;
  ethicalInvestment: boolean | null;
  factFindDate: Date | null;
  investmentExperienceId: number | null;
  isExperiencedFinancialAdviceBefore: boolean | null;
  isLastingPowerOfAttorneyInPlace: number | null;
  isWillInPlace: number | null;
  monthlyRetirementIncomeRequired: number | null;
  niNumber: string | null;
  regiousRestrictions: boolean | null;
  retirementAge: number | null;
  statePension: boolean | null;
}

export interface ClientAdvisor {
  id: number;
  firstName: string;
  lastName: string;
}

export const extractClientsFromClientProfile = (
  profile: Client,
): Pick<Client, 'id' | 'firstName' | 'lastName'>[] => [
  {
    id: profile.id,
    firstName: profile.firstName,
    lastName: profile.lastName,
  },
  ...profile.linkedClients.map((link) => ({
    id: link.linkedClientId as ClientId,
    firstName: link.firstName || '',
    lastName: link.lastName,
  })),
];
