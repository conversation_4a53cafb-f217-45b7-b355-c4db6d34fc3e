import { ClientId } from 'src/modules/clients';
import { apiClient } from 'src/services/api';
import { DateTime } from 'src/utils/dateTime';
import { Period } from '../types/Period';
import { PortfolioValuation } from '../types/PortfolioValuation';

type Payload = {
  period: Period;
  from_date: string;
  to_date?: string;
};

type Response = Array<{
  timestamp: string;
  asset_valuation: number;
  debt_valuation: number;
  net_valuation: number;
}>;

export default async (
  clientId: ClientId,
  period: Period,
  fromDate: string,
  toDate?: string,
): Promise<Array<PortfolioValuation>> => {
  const payload: Payload = {
    period,
    from_date: fromDate,
  };

  if (toDate) {
    payload.to_date = toDate;
  }

  const response = await apiClient.get<Promise<Response>>(
    `/api/v1/clients/${clientId}/portfolio-valuations`,
    payload,
  );

  return response.map((valuation) => ({
    timestamp: new DateTime(valuation.timestamp),
    assetValuation: valuation.asset_valuation,
    debtValuation: valuation.debt_valuation,
    netValuation: valuation.net_valuation,
  }));
};
