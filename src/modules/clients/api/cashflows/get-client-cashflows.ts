import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import { Cashflow } from '../../models';

interface ClientHoldingDTO {
  id: number;
  amount: number;
  period: number;
  income_expenditure_type_id: number;
}

export const getClientCashflows = async (id: ClientId): Promise<Cashflow[]> => {
  const dtos = await apiClient.get<ClientHoldingDTO[]>(
    `/api/v1/clients/${id}/cashflows`,
  );

  return dtos.map((dto) => ({
    id: dto.id,
    amount: dto.amount,
    period: dto.period,
    incomeExpenditureTypeId: dto.income_expenditure_type_id,
  }));
};
