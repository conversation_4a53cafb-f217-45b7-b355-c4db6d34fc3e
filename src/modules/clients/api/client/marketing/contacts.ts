import { apiClient } from 'src/services/api';
import { Nullable } from 'src/types/Common';
import { ClientId } from 'src/modules/clients';
import { ContactDTO } from 'src/modules/factfind/dtos/marketing.dto';

type TContact = {
  id: string;
  email: string;
};

type ContactData = {
  email: string;
  firstName: string;
  lastName: string;
  phone: Nullable<string>;
};

type CreateContact = ContactData;
type CreateContactResponse = {
  contact: TContact;
};

export type ClientMarketingData = ContactDTO;

export const getContact = async (
  clientId: ClientId,
): Promise<ClientMarketingData> => {
  return await apiClient.get(`/api/v1/clients/${clientId}/marketing/contact`);
};

export const createContact = async (
  clientId: ClientId,
  data: CreateContact,
): Promise<TContact> => {
  const { contact } = await apiClient.post<
    CreateContact,
    Promise<CreateContactResponse>
  >(`/api/v1/clients/${clientId}/marketing/contact`, data);

  return contact;
};

type TUpdateContact = { id: number } & ContactData;

export const updateContact = async (
  clientId: ClientId,
  data: TUpdateContact,
): Promise<void> => {
  await apiClient.put<TUpdateContact, Promise<void>>(
    `/api/v1/clients/${clientId}/marketing/contact`,
    data,
  );
};
