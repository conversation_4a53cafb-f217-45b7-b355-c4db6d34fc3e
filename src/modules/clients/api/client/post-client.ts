import { Nullable } from 'src/types/Common';
import { apiClient } from 'src/services/api';
import { DateTime } from 'src/utils/dateTime';
import { Client } from '../../models';
import { ClientDTO, LinkDTO } from '../../dtos';

type ErrorDetail<TDataModel> = Partial<Record<keyof TDataModel, string>>;

// TODO: Fix
type FormSubmissionError = {
  response: {
    data: {
      detail: Array<ErrorDetail<PostClientDTO>>;
    };
  };
};

const isFormSubmissionError = (e: any): e is FormSubmissionError => {
  return 'detail' in e.response.data;
};

export class FormFieldsError extends Error {
  constructor(public formErrors: ErrorDetail<PostClientPayload>) {
    super();
  }
}

interface PostClientPayload
  extends Pick<
    Client,
    | 'clientType'
    | 'title'
    | 'firstName'
    | 'lastName'
    | 'dateOfBirth'
    | 'phoneNumber'
    | 'mobileNumber'
    | 'clientSource'
    | 'clientStatus'
    | 'noEmailReasonId'
    | 'linkedClients'
  > {
  clientOwner: Client['advisor']['id'];
  email: Nullable<string>;
}

interface PostClientDTO
  extends Pick<
    ClientDTO,
    | 'client_type_id'
    | 'title_id'
    | 'first_name'
    | 'last_name'
    | 'email'
    | 'no_email_reason_id'
    | 'client_status'
    | 'client_source'
    | 'owner_id'
    | 'date_of_birth'
    | 'phone_number'
    | 'mobile_number'
  > {
  links: Pick<LinkDTO, 'link_relationship_id' | 'client_id'>[];
}

export const postClient = async (
  payload: PostClientPayload,
): Promise<Client['id']> => {
  const postClientDTO: PostClientDTO = {
    client_source: payload.clientSource,
    client_status: payload.clientStatus,
    client_type_id: payload.clientType,
    date_of_birth: payload.dateOfBirth
      ? new DateTime(payload.dateOfBirth).formatForForm()
      : null,
    email: payload.email,
    no_email_reason_id: payload.noEmailReasonId,
    first_name: payload.firstName,
    last_name: payload.lastName,
    phone_number: payload.phoneNumber,
    mobile_number: payload.mobileNumber,
    owner_id: payload.clientOwner,
    title_id: payload.title,
    links: payload.linkedClients.map((linked) => ({
      link_relationship_id: linked.linkTypeId,
      client_id: linked.linkedClientId,
    })),
  };

  try {
    return await apiClient.post<PostClientDTO, Client['id']>(
      `/api/v1/clients`,
      postClientDTO,
    );
  } catch (e: unknown) {
    if (isFormSubmissionError(e)) {
      const error = Object.assign(
        {},
        e.response.data.detail,
      ) as ErrorDetail<PostClientDTO>;

      throw new FormFieldsError({
        clientType: error.client_type_id,
        clientOwner: error.owner_id,
        clientSource: error.client_source,
        clientStatus: error.client_status,
        dateOfBirth: error.date_of_birth,
        email: error.email,
        noEmailReasonId: error.no_email_reason_id,
        firstName: error.first_name,
        lastName: error.last_name,
        phoneNumber: error.phone_number,
        mobileNumber: error.mobile_number,
        title: error.title_id,
      });
    }

    throw e;
  }
};
