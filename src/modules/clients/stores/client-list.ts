import { defineStore } from 'pinia';
import { QueryParams } from 'src/types/api';
import getClients from '../api/client/get-clients';
import { ClientList } from '../models';

export type ClientListState = {
  clients: ClientList['clients'];
  totalClientCount: ClientList['totalClientCount'];
  error: Error | null;
};

type Actions = {
  fetchClients: (queryParams: QueryParams) => Promise<void>;
};

export const useStore = defineStore<
  'client_list',
  ClientListState,
  any,
  Actions
>('client_list', {
  state: () => ({
    clients: [],
    totalClientCount: 0,
    error: null,
  }),
  actions: {
    async fetchClients(queryParams: QueryParams): Promise<void> {
      try {
        const payload = await getClients(queryParams);
        this.clients = payload.clients;
        this.totalClientCount = payload.totalClientCount;
      } catch (e: unknown) {
        if (e instanceof Error) {
          this.error = e;
        } else {
          this.error = new Error('Something unexpected happens.');
        }
      }
    },
  },
});
