import { Nullable } from 'src/types/Common';

export interface ClientHoldingDTO {
  id: number;
  product: ProductDTO | null;
  account_number: string;
  sub_account_number: string;
  clients: Array<{
    id: number;
    first_name: string;
    last_name: string;
    email: Nullable<string>;
  }>;
  provider: {
    id: number;
    name: string;
  };
  fee_split_template: Nullable<number>;
}

export interface ProductDTO {
  attributes: HoldingDTOAttributes;
  current_valuation: ProductDTOValuation;
  product_type: {
    name: string;
  };
}

export interface HoldingDTOAttributes {
  account_number: string | null;
  sub_account_number: string | null;
}

export interface ProductDTOValuation {
  value: number;
  valuation_date: Date | null;
}
