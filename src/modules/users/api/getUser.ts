import { apiClient } from 'src/services/api';
import { User, UserDTO } from '../types/User';

export async function getUser(): Promise<User> {
  const userDTO = await apiClient.get<UserDTO>('/api/v1/user/me');

  return {
    id: userDTO.id,
    email: userDTO.email,
    firstName: userDTO.first_name,
    lastName: userDTO.last_name,
    type: userDTO.type,
    permissions: userDTO.permissions,
  };
}
