import { defineStore } from 'pinia';
import { signOut } from 'aws-amplify/auth';
import { createMongoAbility } from '@casl/ability';

import { UserRole } from 'src/modules/auth';
import { getUser } from 'src/modules/users/api';
import { getUserGroups } from 'src/utils/user';

import { Action, Subject, User, UserAbility, UserId } from '../types/';
//

export type UserState = {
  user: User;
  ability: UserAbility;
};

type Actions = {
  getUserData: () => Promise<User>;
  setUserGroups: (groups: UserRole[]) => void;
  signOut: () => Promise<void>;
};

type Getters = {
  userId: (state: UserState) => UserId | undefined;
  isClient: (state: UserState) => boolean;
  isAdvisor: (state: UserState) => boolean;
  isSuperAdmin: (state: UserState) => boolean;
  getAbility: (state: UserState) => UserState['ability'];
};

// TODO:
// T<PERSON> is complaining here probably due to some overloads issues,
// so silence it for now to keep the type hinting working in .can()/.cannot()
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
export const useUserStore = defineStore<'user', UserState, Getters, Actions>(
  'user',
  {
    state: (): UserState => ({
      user: {} as User,
      ability: createMongoAbility(),
    }),
    getters: {
      userId: (state) => state.user?.id,
      isClient: (state) => state.user.groups?.includes(UserRole.Client),
      isAdvisor: (state) => state.user.groups?.includes(UserRole.Adviser),
      isSuperAdmin: (state) => state.user.groups?.includes(UserRole.SuperAdmin),
      getAbility: (state) => state.ability,
    },
    actions: {
      async getUserData() {
        try {
          const { permissions, ...user } = await getUser();

          // set user
          this.user = user as User;

          // set user groups
          this.user.groups = await getUserGroups();

          // set user abilities
          this.ability = createMongoAbility<[Action, Subject]>(permissions, {
            detectSubjectType: (object) => object,
          });
        } catch (error) {
          if (error instanceof Error) {
            throw new Error(error.message);
          }
          throw new Error("Couldn't sign in");
        }
      },
      async signOut() {
        try {
          return await signOut().then(() => {
            this.user = null;
          });
        } catch (error) {
          console.log('Error signing out: ', error);
        }
      },
    },
  },
);
