import { apiClient } from 'src/services/api';

import { dtoToMessageMapper } from '../utils/mappers/dtoToMessageMapper';
import { AllFormValues, AssistantTextFormValues } from '../types/form-model';
import { generate_placeholder_message_metadata } from '../utils/helpers';
import { Message } from '../types/message';
//

export default async (): Promise<AllFormValues[]> => {
  let response: Message[];

  try {
    response = await apiClient.get<Promise<Message[]>>(
      `/api/v2/chatbot/new-conversation`,
    );
  } catch {
    // Backstop error displayed to user in the event the backend does not send a viable in-chat error message to the front end via the stream and instead sends an HTTP error. As this error message is not persisted in the backend it will disappear on page refresh
    const { id, timestamp } = generate_placeholder_message_metadata();

    return Promise.resolve([
      {
        id: id,
        timestamp: timestamp,
        messageLayout: 'assistant_text_layout',
        message:
          'Unfortunately our server had an issue creating a new conversation. Please try again in a moment.',
      } as AssistantTextFormValues,
    ]);
  }

  return Promise.resolve(
    response.map((dto: Message) => dtoToMessageMapper(dto)),
  );
};
