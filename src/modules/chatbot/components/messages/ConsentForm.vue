<template>
  <BaseChatDataCard
    title="Data Access Consent"
    :is-user="false"
    width="default"
  >
    <div v-if="!isDenied && !isGranted" data-testid="initial-form">
      <p class="mb-4 text-sm text-gray-600" data-testid="initial-description">
        Before proceeding, <PERSON><PERSON><PERSON> needs your permission to access your personal
        data to provide personalised assistance and recommendations. This
        includes access to your profile information, preferences, and relevant
        data necessary for our services.
      </p>

      <div class="flex flex-col gap-2 sm:flex-row sm:gap-3">
        <button
          class="bg-primary hover:bg-primary-700 rounded-md px-4 py-2 text-sm font-medium text-white transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
          @click="handleAccept"
          :disabled="props.disabled || isGranting"
          data-testid="grant-button"
        >
          {{ isGranting ? 'Granting...' : 'Grant Permission' }}
        </button>

        <button
          class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          @click="handleDeny"
          :disabled="props.disabled || isGranting"
          data-testid="deny-button"
        >
          Deny
        </button>
      </div>

      <div
        v-if="grantError"
        class="mt-3 text-sm text-red-600"
        data-testid="grant-error"
      >
        Failed to grant consent. Please try again.
      </div>
    </div>

    <div v-else-if="isGranted" data-testid="granted-message">
      <h3
        class="mb-3 text-lg font-semibold text-green-700"
        data-testid="granted-title"
      >
        Permission Granted Successfully
      </h3>

      <p class="mb-4 text-sm text-gray-600" data-testid="granted-description">
        Thank you for granting permission. Avril can now access your personal
        data to provide you with personalised assistance and recommendations.
        You can now start chatting!
      </p>
      <p class="mb-4 text-sm text-gray-600" data-testid="granted-additional">
        You can withdraw your consent at any time by asking Avril.
      </p>

      <div class="flex">
        <button
          class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          @click="handleContinue"
          :disabled="props.disabled"
          data-testid="continue-button"
        >
          Continue
        </button>
      </div>
    </div>

    <div v-else data-testid="denied-message">
      <h3
        class="mb-3 text-lg font-semibold text-red-700"
        data-testid="denied-title"
      >
        Permission Denied
      </h3>

      <p class="mb-4 text-sm text-gray-600" data-testid="denied-description">
        You are unable to chat with Avril without granting permission for your
        personal data to be accessed. This permission is required to provide you
        with personalised assistance and recommendations.
      </p>

      <div class="flex">
        <button
          class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          @click="handleBack"
          :disabled="props.disabled"
          data-testid="back-button"
        >
          Back
        </button>
      </div>
    </div>
  </BaseChatDataCard>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import { fetchAuthSession } from 'aws-amplify/auth';
  import { ConsentFormValues } from '../../types/form-model';
  import { useUserStore } from 'src/modules/users/stores/userStore';
  import { useChatbotStore } from '../../stores/chatbotStore';
  import grantConsent from '../../api/grant-consent';
  import BaseChatDataCard from 'src/modules/chatbot/components/messages/BaseChatDataCard.vue';

  const props = defineProps<{
    message: ConsentFormValues;
    disabled?: boolean;
  }>();

  const isDenied = ref(false);
  const isGranted = ref(false);
  const isGranting = ref(false);
  const grantError = ref(false);

  const userStore = useUserStore();
  const { userId } = storeToRefs(userStore);

  const chatbotStore = useChatbotStore();

  const handleAccept = async () => {
    if (!userId.value || typeof userId.value !== 'number') {
      return;
    }

    try {
      isGranting.value = true;
      grantError.value = false; // Reset error state
      await grantConsent(userId.value);

      // Force refresh JWT token from Cognito to get updated groups
      await fetchAuthSession({ forceRefresh: true });

      // Refresh user data to get updated user info and abilities
      await userStore.getUserData();

      isGranted.value = true;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      grantError.value = true;
    } finally {
      isGranting.value = false;
    }
  };

  const handleDeny = () => {
    isDenied.value = true;
  };

  const handleBack = () => {
    isDenied.value = false;
  };

  const handleContinue = async () => {
    chatbotStore.removeMessage(props.message.id);
    await chatbotStore.fetchMessages({ force: true });
  };
</script>
