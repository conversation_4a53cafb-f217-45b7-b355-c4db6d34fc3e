<template>
  <div class="mb-2 flex">
    <div class="min-w-16 grow" />
    <div
      class="bg-primary relative max-w-xs px-4 py-2 text-white after:absolute after:bottom-0 after:right-[-8px] after:size-2 after:bg-[#215249] after:content-[''] after:[clip-path:polygon(0%_0%,0%_100%,100%_100%)] md:max-w-md"
      style="border-radius: 16px 16px 0px 16px"
    >
      <p class="whitespace-pre-wrap text-sm font-medium">
        {{ props.message.message }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { UserTextFormValues } from '../../types/form-model';

  const props = defineProps<{
    message: UserTextFormValues;
  }>();
</script>
