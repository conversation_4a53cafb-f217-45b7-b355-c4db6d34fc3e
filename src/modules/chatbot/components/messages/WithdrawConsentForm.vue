<template>
  <BaseChatDataCard
    title="Withdraw Data Access Permission"
    :is-user="false"
    width="default"
  >
    <div
      v-if="!showConfirmation && !showCancelMessage"
      data-testid="initial-form"
    >
      <p class="mb-4 text-sm text-gray-600" data-testid="initial-description">
        Withdrawing your consent will prevent <PERSON><PERSON><PERSON> from accessing your personal
        data. This means you will no longer be able to chat with <PERSON><PERSON><PERSON> as it
        will not be able to access your data and make personalised
        recommendations.
      </p>

      <div class="flex flex-col gap-2 sm:flex-row sm:gap-3">
        <button
          class="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
          @click="handleConfirm"
          :disabled="props.disabled || isWithdrawing"
          data-testid="confirm-button"
        >
          {{ isWithdrawing ? 'Withdrawing...' : 'Confirm Withdrawal' }}
        </button>

        <button
          class="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          @click="handleCancel"
          :disabled="props.disabled || isWithdrawing"
          data-testid="cancel-button"
        >
          Cancel
        </button>
      </div>

      <div
        v-if="withdrawalError"
        class="mt-3 text-sm text-red-600"
        data-testid="withdrawal-error"
      >
        Failed to withdraw consent. Please try again.
      </div>
      <div
        v-if="cancellationError"
        class="mt-3 text-sm text-red-600"
        data-testid="cancellation-error"
      >
        Failed to process cancellation. Please try again.
      </div>
    </div>

    <div v-else-if="showCancelMessage" data-testid="cancel-message">
      <h3
        class="mb-3 text-lg font-semibold text-green-700"
        data-testid="cancel-title"
      >
        No Changes Made
      </h3>

      <p class="mb-4 text-sm text-gray-600" data-testid="cancel-description">
        You can still chat with Avril and it will continue to have access to
        your data to provide personalised assistance and recommendations.
      </p>
    </div>

    <div v-else data-testid="confirmation-message">
      <h3
        class="mb-3 text-lg font-semibold text-red-700"
        data-testid="confirmation-title"
      >
        Permission Withdrawal Confirmed
      </h3>

      <p
        class="mb-4 text-sm text-gray-600"
        data-testid="confirmation-description"
      >
        Your data access permission has been withdrawn. Avril will no longer be
        able to access your personal data or provide personalised
        recommendations.
      </p>
      <p
        class="mb-4 text-sm text-gray-600"
        data-testid="confirmation-additional"
      >
        The next time you open chat you will be prompted to grant Avril
        permission to access your personal data.
      </p>

      <div class="flex">
        <button
          class="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          @click="handleExitChat"
          :disabled="props.disabled"
          data-testid="exit-button"
        >
          Exit Chat
        </button>
      </div>
    </div>
  </BaseChatDataCard>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useRouter } from 'vue-router';
  import { fetchAuthSession } from 'aws-amplify/auth';
  import { WithdrawConsentFormValues } from '../../types/form-model';
  import { useUserStore } from 'src/modules/users/stores/userStore';
  import { useChatbotStore } from '../../stores/chatbotStore';
  import BaseChatDataCard from 'src/modules/chatbot/components/messages/BaseChatDataCard.vue';

  const props = defineProps<{
    message: WithdrawConsentFormValues;
    disabled?: boolean;
  }>();

  const showConfirmation = ref(
    props.message.completed && props.message.withdrawn,
  );
  const showCancelMessage = ref(
    props.message.completed && !props.message.withdrawn,
  );
  const isWithdrawing = ref(false);
  const withdrawalError = ref(false);
  const cancellationError = ref(false);

  const router = useRouter();
  const userStore = useUserStore();
  const { userId } = storeToRefs(userStore);
  const chatbotStore = useChatbotStore();
  const { postMessage } = chatbotStore;

  const handleConfirm = async () => {
    if (!userId.value) {
      return;
    }

    try {
      isWithdrawing.value = true;
      withdrawalError.value = false; // Reset error state
      props.message.completed = true;
      props.message.withdrawn = true;
      await postMessage(props.message);

      // Force refresh JWT token from Cognito to get updated groups
      await fetchAuthSession({ forceRefresh: true });

      // Refresh user data to get updated user info and abilities
      await userStore.getUserData();

      showConfirmation.value = true;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      withdrawalError.value = true;
      props.message.completed = false;
      props.message.withdrawn = false;
      showConfirmation.value = false;
    } finally {
      isWithdrawing.value = false;
    }
  };

  const handleCancel = async () => {
    try {
      cancellationError.value = false; // Reset error state
      showCancelMessage.value = true;
      props.message.completed = true;
      props.message.withdrawn = false;
      await postMessage(props.message);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      cancellationError.value = true;
      showCancelMessage.value = false;
      props.message.completed = false;
      props.message.withdrawn = false;
    }
  };

  const handleExitChat = () => {
    // Exit the chat screen first
    router.go(-1);
    // Clear the chat after navigation so user doesn't see the reset
    chatbotStore.newConversation();
  };
</script>
