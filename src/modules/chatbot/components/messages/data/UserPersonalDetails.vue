<template>
  <BaseChatDataForm
    :config="formConfig"
    :message="props.message"
    :validation-schema="validationSchema['user__personal_details_layout']"
    :initial-values="getInitialValues('user__personal_details_layout')"
    :on-submit="handleFormSubmit"
    :field-overrides="fieldOverrides"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useChatbotStore } from '../../../stores/chatbotStore';
  import {
    User_PersonalDetails_FormValues,
    getInitialValues,
  } from '../../../types/form-model';
  import { validationSchema } from '../../../services/validation/form-validation';
  import BaseChatDataForm from 'src/modules/chatbot/components/messages/BaseChatDataForm.vue';
  import {
    FieldOverrides,
    FormConfig,
    HttpMethod,
  } from 'src/modules/chatbot/types/form-config';

  const chatbotStore = useChatbotStore();
  const { postMessage } = chatbotStore;

  const props = defineProps<{
    message: User_PersonalDetails_FormValues;
  }>();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id, timestamp, messageLayout, availableFields, ...dataFields } =
    props.message;

  // Field overrides for custom field types
  const fieldOverrides = computed<FieldOverrides>(() => ({
    dateOfBirth: {
      type: 'date',
    },
    email: {
      type: 'email',
    },
  }));

  // Form configuration
  const formConfig = computed<FormConfig>(() => ({
    title:
      props.message.method !== HttpMethod.GET
        ? 'Confirm Change'
        : 'Requested Details',
    validationSchema: 'user__personal_details_layout',
  }));

  const handleFormSubmit = async (
    formValues: User_PersonalDetails_FormValues,
  ) => {
    await postMessage(props.message);
  };
</script>
