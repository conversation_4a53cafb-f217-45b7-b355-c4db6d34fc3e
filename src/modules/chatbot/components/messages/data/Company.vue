<template>
  <BaseChatDataCard
    title="Aventur Company Details"
    v-if="hasData"
    :is-user="false"
  >
    <p
      v-for="item in displayableItems"
      :key="item.key"
      class="mb-2 whitespace-pre-wrap px-4 py-0 text-sm font-medium text-gray-800"
    >
      {{ item.label }}: {{ item.value }}
    </p>
  </BaseChatDataCard>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Company_FormValues } from '../../../types/form-model';
  import BaseChatDataCard from 'src/modules/chatbot/components/messages/BaseChatDataCard.vue';

  const props = defineProps<{
    message: Company_FormValues;
  }>();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, prettier/prettier
  const { id, timestamp, messageLayout, availableFields, method, ...companyDataFields } =
    props.message;

  const displayableItems = computed(() => {
    return Object.entries(companyDataFields)
      .filter(
        ([key, value]) => value !== undefined && value !== null && value !== '',
      )
      .map(([key, value]) => {
        const label = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/^./, (str) => str.toUpperCase());
        return { key, value, label };
      });
  });

  const hasData = computed(() => displayableItems.value.length > 0);
</script>
