import { computed, ref } from 'vue';
import { Asset, Debt } from 'src/modules/factfind/types';

export const useAssetsDebtsFiltering = <T extends object = Asset | Debt>(
  listGetter: () => T[],
  filterFunc: (item: T) => boolean,
) => {
  const includeInactive = ref(false);
  const items = computed(() =>
    listGetter().filter((item: T) => includeInactive.value || filterFunc(item)),
  );
  const disableToggleInactive = computed(
    () => listGetter().length === items.value.length && !includeInactive.value,
  );

  return {
    items,
    includeInactive,
    disableToggleInactive,
  };
};
