import { computed, ref } from 'vue';
import { ClientGoal } from 'src/modules/clients/models';

export const useGoalsFiltering = <T extends object = ClientGoal>(
  listGetter: () => T[],
  filterFunc: (item: T) => boolean,
  defaultIncludeInactive?: boolean,
) => {
  const includeInactive = ref(defaultIncludeInactive ?? false);
  const items = computed(() =>
    listGetter().filter((item: T) => includeInactive.value || filterFunc(item)),
  );
  const disableToggleInactive = computed(
    () => listGetter().length === items.value.length && !includeInactive.value,
  );

  return {
    items,
    includeInactive,
    disableToggleInactive,
  };
};
