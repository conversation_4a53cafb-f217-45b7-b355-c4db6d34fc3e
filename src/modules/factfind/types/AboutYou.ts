import { Nullable } from 'src/types/Common';
import { IDateTime } from 'src/utils/dateTime';
import { Relation } from 'src/types/Relationship';
import { Client } from 'src/modules/clients/models';
import { Address } from 'src/modules/factfind';
//

type PersonalDetails = {
  firstName: Nullable<Client['firstName']>;
  lastName: Nullable<Client['lastName']>;
  dateOfBirth: Nullable<IDateTime>;
  genderId: Nullable<Client['genderId']>;
  maritalStatusId: Nullable<Client['maritalStatusId']>;
  nationalityId: Nullable<Client['nationalityId']>;
  birthCountryId: Nullable<Client['birthCountryId']>;
  primaryCountryId: Nullable<Client['primaryCountryId']>;
  secondaryCountryId: Nullable<Client['secondaryCountryId']>;
  titleId: Nullable<Client['title']>;
};

type ContactDetails = {
  email: Nullable<Client['email']>;
  phoneNumber: Nullable<Client['phoneNumber']>;
  mobileNumber: Nullable<Client['mobileNumber']>;
  addresses: Address[];
};

interface FamilyMember {
  id: Nullable<number>;
  firstName: Nullable<Relation['firstName']>;
  lastName: Nullable<Relation['lastName']>;
  dateOfBirth: Nullable<Relation['dateOfBirth']>;
  relationshipType: Nullable<string>;
}

interface FurtherInformations {
  previousFinancialAdvice: Nullable<boolean>;
  previousInvestmentExperience: Nullable<string>;
  ethicalInvestments: Nullable<boolean>;
  religiousRestrictions: Nullable<boolean>;
  isVulnerablePerson: Nullable<boolean>;
  insuranceNumber: Nullable<string>;
  will: Nullable<string>;
  powerOfAttorney: Nullable<string>;
  creditHistory: Nullable<string>;
  employmentStatus: Nullable<string>;
}

interface RetirementDetails {
  retirementAge: Nullable<number>;
  monthlyRetirementIncomeRequired: Nullable<number>;
  statePension: Nullable<boolean>;
  alreadyRetired: Nullable<boolean>;
}

export interface AboutYou {
  personalDetails: PersonalDetails;
  contactDetails: ContactDetails;
  familyMembers: FamilyMember[];
  furtherInformations: FurtherInformations;
  retirementDetails: RetirementDetails;
}
