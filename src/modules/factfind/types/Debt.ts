import { IMoney } from 'src/utils/money';
import { IDateTime } from 'src/utils/dateTime';
import { Nullable } from 'src/types/Common';
import { Advisor } from 'src/modules/advisors';
import { ClientId } from 'src/modules/clients';
import { ListElement } from 'src/composables/useListCrud';
import { Valuation } from 'src/modules/factfind/models';

export interface BaseDebt {
  id: Nullable<number>;
  clientIds: ClientId[];
  groupId: number;
  typeId: number;
  statusId?: number;
  hasQuantity: boolean;
  providerId: number;
  accountNumber: string;
  valuation: Valuation | null;
  pendingValuations?: Valuation[];
}

export interface Mortgage extends BaseDebt {
  securedAgainstAddressId: Nullable<number>;
  mortgageEndDate: Nullable<IDateTime>;
  interestRate: Nullable<number>;
  mortgageProductEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface PersonalLoan extends BaseDebt {
  securedAgainstAddressId: Nullable<number>;
  loanEndDate: Nullable<IDateTime>;
  interestRate: Nullable<number>;
  loanProductEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface CreditCard extends BaseDebt {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export interface OtherDebt extends BaseDebt {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<IDateTime>;
  monthlyPayment: Nullable<IMoney>;
  advisorId: Nullable<Advisor['id']>;
}

export type Debt = Mortgage | PersonalLoan | CreditCard | OtherDebt;

export type DebtListItem = ListElement<Debt>;
