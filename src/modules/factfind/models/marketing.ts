import { GoalId } from 'src/modules/goals';
import { ClientStatus } from 'src/modules/clients';
import { SubscriptionStatus } from '../types';

export type Contact = {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  contactLists: number[];
};

export type Subscription = {
  id: string;
  status: SubscriptionStatus;
  sourceid: number;
  list: string;
};

export type ContactSubscription = Pick<Subscription, 'status'> &
  Omit<ContactList, 'stringid'>;

export type ContactList = {
  id: string;
  name: string;
  stringid: string;
  compulsory: boolean;
  recommended: boolean;
  required_status: [ClientStatus];
  required_goals: [GoalId];
};
