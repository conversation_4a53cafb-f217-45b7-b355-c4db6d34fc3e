import { ListElement } from 'src/utils/list';
import { IMoney } from 'src/utils/money';
import { Nullable } from 'src/types/Common';
import { IFrequency } from './frequency';

export interface Expenditure {
  id: Nullable<number>;
  typeGroup: number;
  type: number;
  description: string;
  frequency: IFrequency;
  amount: IMoney;
  isEssential: boolean;
}

export type ExpenditureListItem = ListElement<Expenditure>;
