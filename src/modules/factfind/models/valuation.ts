import { IMoney, formatWithCurrency } from 'src/utils/money';
import { IDateTime } from 'src/utils/dateTime';
import { ValuationType } from 'src/modules/factfind/types';

export interface Valuation {
  id?: number;
  date: IDateTime;
  amount: IMoney;
  type: ValuationType;
}

export const formatToString = (valuation: Valuation) => {
  return `${formatWithCurrency(
    valuation.amount,
  )}, ${valuation.date.formatToView()}`;
};

export const formatToMoney = (valuation: Valuation) => {
  return formatWithCurrency(valuation.amount, {
    maximumFractionDigits: 2,
  });
};
