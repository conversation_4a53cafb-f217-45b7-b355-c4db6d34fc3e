import { Asset, Debt } from 'src/modules/factfind/types';
import { useRefData } from 'src/stores';

export function useProductData() {
  const { getProductById, getProductGroupById, getProviderByID } = useRefData();

  function productDisplayText(item: Asset | Debt) {
    return item['providerId']
      ? `${getProviderByID(item['providerId']).name} : ${item['accountNumber']}`
      : '';
  }

  function productTypeName(item: Asset | Debt) {
    if ('nameOfAsset' in item) {
      return item.nameOfAsset;
    }
    return getProductById(item.typeId) ? getProductById(item.typeId).name : '-';
  }

  function productGroupName(item: Asset | Debt) {
    return getProductGroupById(item.groupId)
      ? getProductGroupById(item.groupId).name
      : '-';
  }

  return {
    productDisplayText,
    productGroupName,
    productTypeName,
  };
}
