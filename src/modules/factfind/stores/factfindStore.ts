import { defineStore } from 'pinia';
import { find } from 'lodash';

import { ClientId } from 'src/modules/clients';
import { AboutYou } from 'src/modules/factfind/types/AboutYou';
import { Asset, Debt } from 'src/modules/factfind/types';
import {
  getAssetsAndDebts,
  getClientAboutYou,
  getExpenditures,
  getIncomes,
} from 'src/modules/factfind/api';
import { Expenditure, Income } from 'src/modules/factfind';
//

type ClientFactfind = {
  primaryDetails: AboutYou;
  assets: Asset[];
  debts: Debt[];
  incomes: Income[];
  expenditures: Expenditure[];
};

type Getters = {
  personalDetails: (state: ClientFactfind) => AboutYou['personalDetails'];
  contactDetails: (state: ClientFactfind) => AboutYou['contactDetails'];
  familyMembers: (state: ClientFactfind) => AboutYou['familyMembers'];
  getAssets: (state: ClientFactfind) => Asset[];
  getDebts: (state: ClientFactfind) => Debt[];
  getIncomes: (state: ClientFactfind) => Income[];
  getExpenditures: (state: ClientFactfind) => Expenditure[];
};

type Actions = {
  getClientPrimaryDetails: (clientId: ClientId) => Promise<AboutYou>;
  setClientPrimaryDetails: (data: Partial<AboutYou>) => void;
  loadAssetsAndDebts: (
    clientId: ClientId,
  ) => Promise<{ assets: Asset[]; debts: Debt[] }>;
  setAssets: (assets: Asset[]) => void;
  setDebts: (debts: Debt[]) => void;
  getAssetById: (id: number) => Asset | undefined;
  getDebtById: (id: number) => Debt | undefined;
  loadClientIncomes: (clientId: ClientId) => Promise<Income[]>;
  getIncomeById: (id: number) => Income | undefined;
  loadClientExpenditures: (clientId: ClientId) => Promise<Income[]>;
  getExpenditureById: (id: number) => Expenditure | undefined;
};

export const useFactfindStore = defineStore<
  'factfind',
  ClientFactfind,
  Getters,
  Actions
>('factfind', {
  state: () => ({
    primaryDetails: {} as AboutYou,
    assets: [],
    debts: [],
    incomes: [],
    expenditures: [],
    goals: [], // TODO: Move from ClientStore
  }),
  getters: {
    personalDetails: (state) => state.primaryDetails.personalDetails,
    contactDetails: (state) => state.primaryDetails.contactDetails,
    familyMembers: (state) => state.primaryDetails.familyMembers,
    getAssets: (state) => state.assets,
    getDebts: (state) => state.debts,
    getIncomes: (state) => state.incomes,
    getExpenditures: (state) => state.expenditures,
  },
  actions: {
    async getClientPrimaryDetails(clientId: ClientId) {
      return await getClientAboutYou(clientId);
    },
    setClientPrimaryDetails(data: Partial<AboutYou>) {
      this.primaryDetails = data;
    },
    // assets & debts
    async loadAssetsAndDebts(clientId: ClientId) {
      const { assets, debts } = await getAssetsAndDebts(clientId);
      this.assets = assets;
      this.debts = debts;
      return { assets, debts };
    },
    setAssets(assets: Asset[]) {
      this.assets = assets;
    },
    setDebts(debts: Debt[]) {
      this.debts = debts;
    },
    getAssetById(id: number) {
      return find(this.assets, { id });
    },
    getDebtById(id: number) {
      return find(this.debts, { id });
    },
    // incomes
    async loadClientIncomes(clientId: ClientId) {
      this.incomes = await getIncomes(clientId);
      return this.incomes;
    },
    getIncomeById(id: number) {
      return find(this.incomes, { id });
    },
    // expenditures
    async loadClientExpenditures(clientId: ClientId) {
      this.expenditures = await getExpenditures(clientId);
      return this.expenditures;
    },
    getExpenditureById(id: number) {
      return find(this.expenditures, { id });
    },
  },
});
