import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';

import { GetIncomeDto, mapper } from '../../dtos/get-income.dto';
import { Income } from '../../models';
//

export default async (clientId: ClientId): Promise<Income[]> => {
  const fetchedData = await apiClient.get<GetIncomeDto[]>(
    `/api/v1/clients/${clientId}/factfind/income`,
  );

  return fetchedData.map(mapper);
};
