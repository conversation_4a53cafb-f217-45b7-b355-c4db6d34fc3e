import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import { type Expenditure } from '../../models';
import { GetExpenditureDto, mapper } from '../../dtos/get-expenditure.dto';

export default async (clientId: ClientId): Promise<Expenditure[]> => {
  const fetchedData = await apiClient.get<GetExpenditureDto[]>(
    `/api/v1/clients/${clientId}/factfind/expenditure`,
  );

  return fetchedData.map(mapper);
};
