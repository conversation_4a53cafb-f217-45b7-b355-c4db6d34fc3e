import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import { type Expenditure } from '../../models';
import {
  UpdateExpenditureDto,
  mapper,
} from '../../dtos/update-expenditure.dto';

export default async (
  clientId: ClientId,
  expenditures: Expenditure[],
): Promise<void> => {
  await apiClient.put<UpdateExpenditureDto, void>(
    `/api/v1/clients/${clientId}/factfind/expenditure`,
    mapper(expenditures),
  );
};
