import { apiClient } from 'src/services/api';
import { ArrayElement, Nullable } from 'src/types/Common';
import { factory as dateTimeFactory } from 'src/utils/dateTime';
import { AboutYou } from 'src/modules/factfind/types/AboutYou';
import { ClientId } from 'src/modules/clients';
import {
  AddressDto,
  FactfindDto,
  RelationDto,
} from 'src/modules/factfind';
//

type Body = {
  date_of_birth: Nullable<string>;
  first_name: Nullable<string>;
  gender_id: Nullable<number>;
  last_name: Nullable<string>;
  marital_status_id: Nullable<number>;
  nationality_id: Nullable<number>;
  birth_country_id: Nullable<number>;
  primary_country_id: Nullable<number>;
  secondary_country_id: Nullable<number>;
  title_id: Nullable<number>;
  email_address: Nullable<string>;
  phone_number: Nullable<string>;
  mobile_number: Nullable<string>;
  addresses: Array<AddressDto>;
  relations: Array<RelationDto>;
  factfind: FactfindDto;
};

const mapAddress = (
  address: ArrayElement<AboutYou['contactDetails']['addresses']>,
) => ({
  id: address.id,
  city: address.city,
  country_id: address.countryId,
  address_line_one: address.addressLineOne,
  address_line_two: address.addressLineTwo,
  address_line_three: address.addressLineThree,
  address_line_four: address.addressLineFour,
  moved_in_date: address.moveInDate
    ? dateTimeFactory(address.moveInDate).formatForForm()
    : null,
  moved_out_date: address.moveOutDate
    ? dateTimeFactory(address.moveOutDate).formatForForm()
    : null,
  post_code: address.postCode,
  is_primary: address.isPrimary ?? false,
});

const mapFamilyMember = (
  familyMember: ArrayElement<AboutYou['familyMembers']>,
) => ({
  id: familyMember.id,
  first_name: familyMember.firstName,
  last_name: familyMember.lastName,
  date_of_birth: familyMember.dateOfBirth
    ? dateTimeFactory(familyMember.dateOfBirth).formatForForm()
    : null,
  relationship_type: familyMember.relationshipType,
});

export const aboutYouToDto = (aboutYou: AboutYou): Body => {
  const {
    personalDetails,
    contactDetails,
    familyMembers,
    furtherInformations,
    retirementDetails,
  } = aboutYou;

  return {
    title_id: personalDetails.titleId,
    first_name: personalDetails.firstName,
    last_name: personalDetails.lastName,
    date_of_birth: personalDetails.dateOfBirth
      ? dateTimeFactory(personalDetails.dateOfBirth).formatForForm()
      : null,
    gender_id: personalDetails.genderId,
    marital_status_id: personalDetails.maritalStatusId,
    nationality_id: personalDetails.nationalityId,
    birth_country_id: personalDetails.birthCountryId,
    primary_country_id: personalDetails.primaryCountryId,
    secondary_country_id: personalDetails.secondaryCountryId,
    email_address: contactDetails.email,
    phone_number: contactDetails.phoneNumber,
    mobile_number: contactDetails.mobileNumber,
    addresses: contactDetails.addresses.map(mapAddress),
    relations: familyMembers.map(mapFamilyMember),
    factfind: {
      credit_history: furtherInformations.creditHistory,
      employment_status: furtherInformations.employmentStatus,
      has_experienced_financial_advice_before:
        furtherInformations.previousFinancialAdvice,
      has_power_of_attorney_in_place: furtherInformations.powerOfAttorney,
      has_will_in_place: furtherInformations.will,
      ni_number: furtherInformations.insuranceNumber,
      previous_investment_experience:
        furtherInformations.previousInvestmentExperience,
      religious_restrictions: furtherInformations.religiousRestrictions,
      vulnerable_person: furtherInformations.isVulnerablePerson,
      wish_to_consider_ethical_investments:
        furtherInformations.ethicalInvestments,
      already_retired: retirementDetails.alreadyRetired,
      monthly_retirement_income_required:
        retirementDetails.monthlyRetirementIncomeRequired,
      retirement_age: retirementDetails.retirementAge,
      state_pension: retirementDetails.statePension,
    },
  };
};

export default async (clientId: ClientId, model: AboutYou): Promise<void> => {
  await apiClient.post<Body, void>(
    `/api/v1/clients/${clientId}/factfind/primary-details`,
    aboutYouToDto(model),
  );
};
