// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { apiClient } from 'src/services/api';
import { Goal } from 'src/modules/clients/models';
import { ClientId } from 'src/modules/clients';

interface Payload {
  goal_id: number | null;
  custom_name: string | null;
}

export const putClientGoals = async (
  id: ClientId,
  payload: Payload,
): Promise<Goal[]> =>
  await apiClient.put(`/api/v1/clients/${id}/goals`, {
    body: payload,
  });
