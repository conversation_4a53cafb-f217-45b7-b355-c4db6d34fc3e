import { apiClient } from 'src/services/api';
import { GoalId } from 'src/modules/goals';
import { ClientId } from 'src/modules/clients';

type GoalObjectivesDTO = {
  goal_objectives: string;
};

export default async (
  clientId: ClientId,
  goalId: GoalId,
  goalObjectivesDTO: GoalObjectivesDTO,
): Promise<void> =>
  await apiClient.patch<GoalObjectivesDTO, Promise<void>>(
    `/api/v1/clients/${clientId}/factfind/goals/${goalId}/objectives`,
    goalObjectivesDTO,
  );
