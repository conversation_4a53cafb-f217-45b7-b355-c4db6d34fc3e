import { array, mixed, number, object, string } from 'yup';

import { apiClient } from 'src/services/api';
import {
  ContributionType,
  GoalId,
  RiskAnswersType,
  RiskLevelType,
  WithdrawalType,
} from 'src/modules/goals';
import { ClientId } from 'src/modules/clients';
import {
  ClientGoal,
  ClientGoalTypeEnum,
} from 'src/modules/clients/models';
import { ClientGoalAttrsMapper } from 'src/modules/clients/utils';

import { GoalAttrsDTO } from '../../dtos/goal.dto';
//

const mapAnswers = (answers: number[]): RiskAnswersType =>
  Object.fromEntries(answers.map((j, i) => ['a' + i, j]));

const mapHoldings = (
  holdings: Array<{
    holding_id: number;
    account_number: string;
    provider_name: string;
    product_type: string;
  }>,
) =>
  holdings.map((holding) => ({
    id: holding.holding_id,
    accountNumber: holding.account_number,
    providerName: holding.provider_name,
    productTypeName: holding.product_type,
  }));

const mapAttrs = (attrs: GoalAttrsDTO) => ClientGoalAttrsMapper.toDomain(attrs);

const clientGoalAttributesSchema = object({
  type_: mixed<ClientGoalTypeEnum>().defined().required(),
  target_amount: number().optional().nullable(),
  target_date: string().optional().nullable(),
});

const clientGoalSchema = object({
  goal_id: number().required(),
  goal_name: string().defined().required(),
  goal_objectives: string().defined().nullable(),
  client_ids: array().of(number().defined()).defined(),
  open_cases: array().of(number().defined()).defined(),
  attributes: clientGoalAttributesSchema.required(),
  linked_holdings: array()
    .of(
      object({
        holding_id: number().required(),
        account_number: string().defined().required(),
        provider_name: string().defined().required(),
        product_type: string().defined().required(),
      }),
    )
    .required(),
  risk_profile: object({
    recommended_risk: mixed<RiskLevelType>().required(),
    risk_attitude: number().required(),
    loss_tolerance: number().required(),
    updated_at: string().required(),
    answers: array().of(number().required()).required(),
  })
    .defined()
    .nullable(),
  cash_forecast: object({
    inflation: number().defined().nullable(),
    risk: mixed<RiskLevelType>().defined().nullable(),
    retirement_age: number().required().nullable(),
    cashflows: array()
      .of(
        object({
          type: mixed<ContributionType | WithdrawalType>().defined(),
          start: string().required(),
          end: string().defined().nullable(),
          amount: number().defined().nullable(),
        }),
      )
      .defined(),
    comments: string().defined().nullable(),
    updated_at: string().required(),
  })
    .defined()
    .nullable(),
});

const getClientGoalValidationSchema = array().of(clientGoalSchema).required();

export const getClientGoals = async (
  clientId: ClientId,
): Promise<ClientGoal[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/clients/${clientId}/factfind/goals`,
  );

  const clientGoals = await getClientGoalValidationSchema.validate(response);

  return clientGoals.map((dto) => ({
    id: dto.goal_id as GoalId,
    goalTypeId: dto.attributes.type_,
    name: dto.goal_name,
    cases: dto.open_cases,
    clientIds: dto.client_ids as ClientId[],
    objectives: dto.goal_objectives,
    attributes: mapAttrs(dto.attributes),
    linkedHoldings: mapHoldings(dto.linked_holdings),
    riskProfile: dto.risk_profile
      ? {
          recommended_risk: dto.risk_profile.recommended_risk,
          risk_attitude: dto.risk_profile.risk_attitude,
          loss_tolerance: dto.risk_profile.loss_tolerance,
          answers: mapAnswers(dto.risk_profile.answers),
          updated_at: dto.risk_profile.updated_at,
        }
      : null,
    cashForecast: dto.cash_forecast
      ? {
          cashflows: dto.cash_forecast.cashflows,
          options: {
            retirementAge: dto.cash_forecast.retirement_age,
            inflationRate: dto.cash_forecast.inflation,
            inflationAdjusted: !!dto.cash_forecast.inflation,
            selectedRisk: dto.cash_forecast.risk ?? null,
          },
          comments: dto.cash_forecast.comments,
          updated_at: dto.cash_forecast.updated_at,
        }
      : null,
  }));
};
