import { apiClient } from 'src/services/api';
import { GoalId } from 'src/modules/goals';
import { ClientId } from 'src/modules/clients';

import { GoalAttrsDTO } from '../../dtos/goal.dto';
//

type Body = {
  goal_name: string;
  client_ids: ClientId[];
  goal_attributes: Required<GoalAttrsDTO>;
};

export default async (
  clientId: ClientId,
  goalId: GoalId,
  payload: Body,
): Promise<void> =>
  await apiClient.patch<Body, Promise<void>>(
    `/api/v1/clients/${clientId}/factfind/goals/${goalId}`,
    payload,
  );
