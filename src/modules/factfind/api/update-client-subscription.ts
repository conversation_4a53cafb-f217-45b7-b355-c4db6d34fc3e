import { apiClient } from 'src/services/api';
import { ClientId } from 'src/modules/clients';
import { SubscriptionResponseDTO } from '../dtos/marketing.dto';

type RequestBody = {
  contact_id: string;
  list_id: string;
};

type ResponseBody = {
  contacts: any[];
  contactList: Record<string, any>;
};

export const subscribe = async (
  clientId: ClientId,
  contactId: string,
  contactListId: string,
): Promise<SubscriptionResponseDTO> => {
  const { contactList } = await apiClient.post<RequestBody, ResponseBody>(
    `/api/v1/clients/${clientId}/marketing/subscribe`,
    {
      contact_id: contactId,
      list_id: contactListId,
    },
  );

  return {
    id: contactList.id,
    status: contactList.status,
  };
};

export const unsubscribe = async (
  clientId: ClientId,
  contactId: string,
  contactListId: string,
): Promise<SubscriptionResponseDTO> => {
  const { contactList } = await apiClient.post<RequestBody, ResponseBody>(
    `/api/v1/clients/${clientId}/marketing/unsubscribe`,
    {
      contact_id: contactId,
      list_id: contactListId,
    },
  );

  return {
    id: contactList.id,
    status: contactList.status,
  };
};
