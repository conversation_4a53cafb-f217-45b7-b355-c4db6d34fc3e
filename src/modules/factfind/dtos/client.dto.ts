import { Nullable } from 'src/types/Common';
import { AddressDto, FactfindDto, RelationDto } from '../dtos';

export interface ClientDto {
  date_of_birth: string;
  first_name: string;
  gender_id: number;
  id: number;
  last_name: string;
  marital_status_id: number;
  nationality_id: number;
  birth_country_id: number;
  primary_country_id: number;
  secondary_country_id: Nullable<number>;
  title_id: number;
  email_address: string;
  phone_number: string;
  mobile_number: string;
  addresses: Array<AddressDto>;
  relations: Array<RelationDto>;
  factfind: FactfindDto;
}
