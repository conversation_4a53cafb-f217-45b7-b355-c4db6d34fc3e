import { Money } from 'src/utils/money';
import { Income } from '../models';
import { FrequencyDto, FrequencyMapper } from './frequency.dto';

export interface GetIncomeDto {
  id: number;
  group_id: number;
  type_id: number;
  description: string;
  frequency: FrequencyDto;
  amount: number;
  is_essential: null;
}

export const mapper = (dto: GetIncomeDto): Income => {
  return {
    id: dto.id,
    typeGroup: dto.group_id,
    type: dto.type_id,
    description: dto.description,
    frequency: FrequencyMapper.toDomain(dto.frequency),
    amount: new Money(dto.amount),
  };
};
