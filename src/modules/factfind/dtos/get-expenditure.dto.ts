import { Money } from 'src/utils/money';
import { Expenditure } from '../models';
import { FrequencyDto, FrequencyMapper } from './frequency.dto';

export type GetExpenditureDto = {
  id: number;
  group_id: number;
  type_id: number;
  description: string;
  frequency: FrequencyDto;
  amount: number;
  is_essential: boolean;
};

export const mapper = (dto: GetExpenditureDto): Expenditure => {
  return {
    id: dto.id,
    typeGroup: dto.group_id,
    type: dto.type_id,
    description: dto.description,
    frequency: FrequencyMapper.toDomain(dto.frequency),
    amount: new Money(dto.amount),
    isEssential: dto.is_essential,
  };
};
