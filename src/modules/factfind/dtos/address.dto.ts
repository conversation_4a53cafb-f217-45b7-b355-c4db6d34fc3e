import { Nullable } from 'src/types/Common';
import { Country } from 'src/types/Country';

export interface AddressDto {
  id?: number;
  address_line_one: Nullable<string>;
  address_line_two: Nullable<string>;
  address_line_three: Nullable<string>;
  address_line_four: Nullable<string>;
  city: Nullable<string>;
  country_id: Nullable<Country['id']>;
  post_code: Nullable<string>;
  moved_in_date: Nullable<string>;
  moved_out_date: Nullable<string>;
  is_primary: Nullable<boolean>;
}
