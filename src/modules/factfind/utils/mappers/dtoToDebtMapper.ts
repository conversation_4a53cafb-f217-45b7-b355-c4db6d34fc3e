import { useProducts } from 'src/composables/useProducts';
import {
  CreditCardDTO,
  MortgageDTO,
  OtherDebtDTO,
  PersonalLoanDTO,
  creditCardSchema,
  mortgageSchema,
  otherDebtSchema,
  personalLoanSchema,
} from 'src/modules/factfind/services/validation/dtoToDeptValidator';
import { Account } from 'src/modules/factfind/types/Asset';
import {
  CreditCard,
  Mortgage,
  OtherDebt,
  PersonalLoan,
} from 'src/modules/factfind/types/Debt';
import { Product } from 'src/modules/refdata/types/Product';
import { DateTime } from 'src/utils/dateTime';
import { Money } from 'src/utils/money';
import { ClientId } from 'src/modules/clients';

interface GetBaseDebtDTO {
  id: number;
  client_ids: number[];
  group_id: number;
  type_id: number;
  status_id: number;
  current_quantity: number;
  provider_id: number;
  account_number: string | null;
  attributes: Record<string, any>;
}

async function mortgageToDomain(dto: GetBaseDebtDTO) {
  const isValid = await mortgageSchema.isValid(dto);
  if (!isValid || !DebtDtoTypeGuard.isMortgage(dto)) return Promise.reject();

  return mapMortgageToDomain(dto);
}

async function personalLoanToDomain(dto: GetBaseDebtDTO) {
  const isValid = await personalLoanSchema.isValid(dto);
  if (!isValid || !DebtDtoTypeGuard.isPersonalLoan(dto))
    return Promise.reject();

  return mapPersonalLoanToDomain(dto);
}

async function creditCardToDomain(dto: GetBaseDebtDTO) {
  const isValid = await creditCardSchema.isValid(dto);
  if (!isValid || !DebtDtoTypeGuard.isCreditCard(dto)) return Promise.reject();

  return mapCreditCardToDomain(dto);
}

async function otherDebtToDomain(dto: GetBaseDebtDTO) {
  const isValid = await otherDebtSchema.isValid(dto);
  if (!isValid || !DebtDtoTypeGuard.isOtherDebt(dto)) return Promise.reject();

  return mapOtherDebtToDomain(dto);
}

export class DebtDtoTypeGuard {
  static isDebtProduct = function (
    debt: GetBaseDebtDTO,
    products: Product[],
  ): boolean {
    return products.map((_) => _.id).includes(debt.type_id);
  };

  static isMortgage = (debt: GetBaseDebtDTO): debt is MortgageDTO => {
    const { getMortgageProducts } = useProducts();
    return this.isDebtProduct(debt, getMortgageProducts.value);
  };

  static isPersonalLoan = (debt: GetBaseDebtDTO): debt is PersonalLoanDTO => {
    const { getPersonalLoanProducts } = useProducts();
    return this.isDebtProduct(debt, getPersonalLoanProducts.value);
  };

  static isCreditCard = (debt: GetBaseDebtDTO): debt is CreditCardDTO => {
    const { getCreditCardProducts } = useProducts();
    return this.isDebtProduct(debt, getCreditCardProducts.value);
  };

  static isOtherDebt = (debt: GetBaseDebtDTO): debt is OtherDebtDTO => {
    const { getOtherDebtProducts } = useProducts();
    return this.isDebtProduct(debt, getOtherDebtProducts.value);
  };
}

function mapValuationToDomain(
  dto: NonNullable<MortgageDTO['valuation']>,
): Account['valuation'] {
  return {
    type: dto.is_actual ? 'actual' : 'estimate',
    date: new DateTime(dto.date),
    amount: new Money(dto.amount),
  };
}

const mapMortgageToDomain = (dto: MortgageDTO): Mortgage => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  accountNumber: dto.account_number ?? '',
  providerId: dto.provider_id,
  advisorId: dto.attributes.mortgage_adviser_id,
  securedAgainstAddressId: dto.attributes.secured_against_address,
  mortgageEndDate: dto.attributes.mortgage_end_date
    ? new DateTime(dto.attributes.mortgage_end_date)
    : null,
  interestRate: dto.attributes.interest_rate,
  mortgageProductEndDate: dto.attributes.mortgage_product_end_date
    ? new DateTime(dto.attributes.mortgage_product_end_date)
    : null,
  monthlyPayment: dto.attributes.monthly_payment
    ? new Money(dto.attributes.monthly_payment)
    : null,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapPersonalLoanToDomain = (dto: PersonalLoanDTO): PersonalLoan => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  accountNumber: dto.account_number ?? '',
  providerId: dto.provider_id,
  advisorId: dto.adviser_id,
  securedAgainstAddressId: dto.attributes.secured_against_address,
  loanEndDate: dto.attributes.loan_end_date
    ? new DateTime(dto.attributes.loan_end_date)
    : null,
  interestRate: dto.attributes.interest_rate,
  loanProductEndDate: dto.attributes.loan_end_date
    ? new DateTime(dto.attributes.loan_product_end_date)
    : null,
  monthlyPayment: dto.attributes.monthly_payment
    ? new Money(dto.attributes.monthly_payment)
    : null,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapCreditCardToDomain = (dto: CreditCardDTO): CreditCard => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  accountNumber: dto.account_number ?? '',
  providerId: dto.provider_id,
  advisorId: dto.adviser_id,
  interestRate: dto.attributes.interest_rate,
  interestRateEndDate: dto.attributes.interest_rate_end_date
    ? new DateTime(dto.attributes.interest_rate_end_date)
    : null,
  monthlyPayment: dto.attributes.monthly_payment
    ? new Money(dto.attributes.monthly_payment)
    : null,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

const mapOtherDebtToDomain = (dto: OtherDebtDTO): OtherDebt => ({
  id: dto.id,
  clientIds: dto.client_ids as ClientId[],
  groupId: dto.group_id,
  typeId: dto.type_id,
  statusId: dto.status_id,
  hasQuantity: Boolean(dto.current_quantity),
  accountNumber: dto.account_number ?? '',
  providerId: dto.provider_id,
  advisorId: dto.adviser_id,
  interestRate: dto.attributes.interest_rate,
  interestRateEndDate: dto.attributes.interest_rate_end_date
    ? new DateTime(dto.attributes.interest_rate_end_date)
    : null,
  monthlyPayment: dto.attributes.monthly_payment
    ? new Money(dto.attributes.monthly_payment)
    : null,
  valuation: dto.valuation ? mapValuationToDomain(dto.valuation) : null,
});

export const dtoToDebtMapper = async (dto: GetBaseDebtDTO) => {
  return Promise.any([
    mortgageToDomain(dto),
    personalLoanToDomain(dto),
    creditCardToDomain(dto),
    otherDebtToDomain(dto),
  ]).catch(() => {
    throw new Error(`Couldn't match debt (ID: ${dto.id}) to any known type`);
  });
};
