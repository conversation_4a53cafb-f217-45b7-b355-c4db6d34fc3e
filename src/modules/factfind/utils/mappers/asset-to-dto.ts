import { Nullable } from 'src/types/Common';
import { ClientId } from 'src/modules/clients';
import { Asset } from 'src/modules/factfind/types';
import { AssetTypeGuard } from 'src/modules/factfind/utils/assetGuard';
import {
  Account,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from 'src/modules/factfind/types/Asset';
import { Country } from 'src/types/Country';
import { ProductType } from 'src/modules/refdata/types/Product';

interface PostBaseAssetDTO {
  id: number | null;
  client_ids: ClientId[];
  group_id: number;
  type_id: number;
  attributes: Record<string, any>;
  pending_valuations: {
    is_actual: boolean;
    date: string;
    amount: number;
  }[];
}

interface AccountDTO extends PostBaseAssetDTO {
  provider_id: number;
  account_number: string;
  sub_account_number: string;
  attributes: {
    product_type: ProductType;
    risk_level: number | null;
    monthly_payment_direction: string | null;
    monthly_payment_amount: number | null;
    cover_amount: number | null;
    policy_end_date: string | null;
    monthly_benefit: number | null;
    deferred_weeks: number | null;
  };
}

interface DefinedBenefitPensionDTO extends PostBaseAssetDTO {
  provider_id: number;
  account_number: string;
  sub_account_number: string;
  attributes: {
    product_type: ProductType;
    index_linked: boolean | null;
    survivor_benefits: boolean | null;
    is_current_job: boolean | null;
    estimated_annual_income_at_retirement: number | null;
    scheme_normal_retirement_age: number | null;
    accrual_rate: number | null;
    predicted_final_salary: number | null;
    predicted_years_of_service_at_retirement: number | null;
  };
}

interface PropertyDTO extends PostBaseAssetDTO {
  attributes: {
    product_type: ProductType;
    address_line_one: string | null;
    address_line_two: string | null;
    city: string | null;
    post_code: string | null;
    country_id: Country['id'];
    ownership: string | null;
  };
}

interface CompanySharesDTO extends PostBaseAssetDTO {
  attributes: {
    product_type: ProductType;
    name_of_company: string | null;
  };
}

interface CryptoCurrencyDTO extends PostBaseAssetDTO {
  attributes: {
    product_type: ProductType;
    name_of_currency: string | null;
    number_of_coins: Nullable<number>;
  };
}

interface OtherAssetDTO extends PostBaseAssetDTO {
  attributes: {
    product_type: ProductType;
    name_of_asset: string | null;
  };
}

export type PostAssetDTO =
  | AccountDTO
  | PropertyDTO
  | CompanySharesDTO
  | CryptoCurrencyDTO
  | OtherAssetDTO
  | DefinedBenefitPensionDTO;

const mapValuation = (
  pendingValuations: NonNullable<Asset['pendingValuations']>,
): PostAssetDTO['pending_valuations'] => {
  return pendingValuations.map((valuation) => ({
    amount: valuation.amount.getValue(),
    date: valuation.date.formatForForm(),
    is_actual: valuation.type === 'actual',
  }));
};

export const mapper = (asset: Asset): PostAssetDTO => {
  if (AssetTypeGuard.isAccount(asset)) {
    return accountToDto(asset, 'account');
  }

  if (AssetTypeGuard.isTermPolicy(asset)) {
    return accountToDto(asset, 'term_policy');
  }

  if (AssetTypeGuard.isIndemnityPolicy(asset)) {
    return accountToDto(asset, 'indemnity_policy');
  }

  if (AssetTypeGuard.isWholeOfLifePolicy(asset)) {
    return accountToDto(asset, 'whole_of_life_policy');
  }

  if (AssetTypeGuard.isIncomeProtectionPolicy(asset)) {
    return accountToDto(asset, 'income_protection_policy');
  }

  if (AssetTypeGuard.isDefinedBenefitPension(asset)) {
    return definedBenefitPensionToDto(asset);
  }

  if (AssetTypeGuard.isProperty(asset)) {
    return propertyToDto(asset);
  }

  if (AssetTypeGuard.isCompanyShares(asset)) {
    return companySharesToDto(asset);
  }

  if (AssetTypeGuard.isCryptoCurrency(asset)) {
    return cryptoCurrencyToDto(asset);
  }

  if (AssetTypeGuard.isOtherAsset(asset)) {
    return otherAssetToDto(asset);
  }

  throw new Error("Couldn't match asset to any known type");
};

const accountToDto = (
  asset: Account,
  productType: ProductType,
): AccountDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  account_number: asset.accountNumber,
  sub_account_number: asset.subAccountNumber,
  provider_id: asset.providerId,
  attributes: {
    product_type: productType,
    risk_level: asset.riskLevel,
    monthly_payment_direction: asset.monthlyPaymentDirection,
    monthly_payment_amount: asset.monthlyPaymentAmount,
    cover_amount: asset.coverAmount,
    policy_end_date: asset.policyEndDate,
    monthly_benefit: asset.monthlyBenefit,
    deferred_weeks: asset.deferredWeeks,
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});

const definedBenefitPensionToDto = (
  asset: DefinedBenefitPension,
): DefinedBenefitPensionDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  account_number: asset.accountNumber,
  sub_account_number: asset.subAccountNumber,
  provider_id: asset.providerId,
  attributes: {
    product_type: 'defined_benefit_pension',
    index_linked: asset.indexLinked,
    survivor_benefits: asset.survivorBenefits,
    is_current_job: asset.isCurrentJob,
    estimated_annual_income_at_retirement:
      asset.estimatedAnnualIncomeAtRetirement,
    scheme_normal_retirement_age: asset.schemeNormalRetirementAge,
    accrual_rate: asset.accrualRate,
    predicted_final_salary: asset.predictedFinalSalary,
    predicted_years_of_service_at_retirement:
      asset.predictedYearsOfServiceAtRetirement,
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});

const propertyToDto = (asset: Property): PropertyDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  attributes: {
    address_line_one: asset.addressLineOne,
    address_line_two: asset.addressLineTwo,
    city: asset.city,
    country_id: asset.countryId,
    ownership: asset.owner,
    post_code: asset.postCode,
    product_type: 'property',
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});

const companySharesToDto = (asset: CompanyShares): CompanySharesDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  attributes: {
    name_of_company: asset.nameOfCompany,
    product_type: 'company_shares',
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});

const cryptoCurrencyToDto = (asset: CryptoCurrency): CryptoCurrencyDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  attributes: {
    name_of_currency: asset.nameOfCurrency,
    number_of_coins: asset.numberOfCoins,
    product_type: 'crypto_currency',
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});

const otherAssetToDto = (asset: OtherAsset): OtherAssetDTO => ({
  id: asset.id,
  client_ids: asset.clientIds,
  group_id: asset.groupId,
  type_id: asset.typeId,
  attributes: {
    name_of_asset: asset.nameOfAsset,
    product_type: 'other_asset',
  },
  pending_valuations: asset.pendingValuations
    ? mapValuation(asset.pendingValuations)
    : [],
});
