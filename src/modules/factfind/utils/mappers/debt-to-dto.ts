import { Nullable } from 'src/types/Common';
import { ClientId } from 'src/modules/clients';
import { Asset, Debt } from 'src/modules/factfind/types';
import { DebtTypeGuard } from 'src/modules/factfind/utils/debtGuard';
import { PostAssetDTO } from 'src/modules/factfind/utils/mappers/asset-to-dto';
import {
  CreditCard,
  Mortgage,
  OtherDebt,
  PersonalLoan,
} from 'src/modules/factfind/types/Debt';
import { ProductType } from 'src/modules/refdata/types/Product';

interface PostBaseDebtDTO {
  id: number | null;
  client_ids: ClientId[];
  group_id: number;
  type_id: number;
  provider_id: number;
  account_number: string;
  attributes: Record<string, any>;
  pending_valuations: {
    is_actual: boolean;
    date: string;
    amount: number;
  }[];
}

interface MortgageDTO extends PostBaseDebtDTO {
  attributes: {
    product_type: ProductType;
    mortgage_adviser_id: Nullable<number>;
    secured_against_address: Nullable<number>;
    mortgage_end_date: Nullable<string>;
    interest_rate: Nullable<number>;
    mortgage_product_end_date: Nullable<string>;
    monthly_payment: Nullable<number>;
  };
}

interface PersonalLoanDTO extends PostBaseDebtDTO {
  adviser_id: number;
  attributes: {
    product_type: ProductType;
    secured_against_address: Nullable<number>;
    loan_end_date: Nullable<string>;
    interest_rate: Nullable<number>;
    loan_product_end_date: Nullable<string>;
    monthly_payment: Nullable<number>;
  };
}

interface CreditCardDTO extends PostBaseDebtDTO {
  adviser_id: number;
  attributes: {
    product_type: ProductType;
    interest_rate: Nullable<number>;
    interest_rate_end_date: Nullable<string>;
    monthly_payment: Nullable<number>;
  };
}

interface OtherDebtDTO extends PostBaseDebtDTO {
  adviser_id: number;
  attributes: {
    product_type: ProductType;
    interest_rate: Nullable<number>;
    interest_rate_end_date: Nullable<string>;
    monthly_payment: Nullable<number>;
  };
}

export type PostDebtDTO =
  | MortgageDTO
  | PersonalLoanDTO
  | CreditCardDTO
  | OtherDebtDTO;

const mapValuation = (
  pendingValuations: NonNullable<Asset['pendingValuations']>,
): PostAssetDTO['pending_valuations'] => {
  return pendingValuations.map((valuation) => ({
    amount: valuation.amount.getValue(),
    date: valuation.date.formatForForm(),
    is_actual: valuation.type === 'actual',
  }));
};

export const mapper = (debt: Debt) => {
  if (DebtTypeGuard.isMortgage(debt)) {
    return mortgageToDto(debt);
  }

  if (DebtTypeGuard.isPersonalLoan(debt)) {
    return personalLoanToDto(debt);
  }

  if (DebtTypeGuard.isCreditCard(debt)) {
    return creditCardToDto(debt);
  }

  if (DebtTypeGuard.isOtherDebt(debt)) {
    return otherDebtToDto(debt);
  }

  throw new Error("Couldn't match debt to any known type");
};

const mortgageToDto = (debt: Mortgage): MortgageDTO => ({
  id: debt.id,
  client_ids: debt.clientIds,
  group_id: debt.groupId,
  type_id: debt.typeId,
  account_number: debt.accountNumber,
  provider_id: debt.providerId,
  attributes: {
    mortgage_adviser_id: debt.advisorId,
    secured_against_address: debt.securedAgainstAddressId,
    mortgage_end_date: debt.mortgageEndDate
      ? debt.mortgageEndDate.formatForForm()
      : debt.mortgageEndDate,
    interest_rate: debt.interestRate,
    mortgage_product_end_date: debt.mortgageProductEndDate
      ? debt.mortgageProductEndDate.formatForForm()
      : null,
    monthly_payment: debt.monthlyPayment?.getValue() ?? null,
    product_type: 'mortgage',
  },
  pending_valuations: debt.pendingValuations
    ? mapValuation(debt.pendingValuations)
    : [],
});

const personalLoanToDto = (debt: PersonalLoan): PersonalLoanDTO => ({
  id: debt.id,
  client_ids: debt.clientIds,
  group_id: debt.groupId,
  type_id: debt.typeId,
  account_number: debt.accountNumber,
  provider_id: debt.providerId,
  adviser_id: debt.advisorId as number,
  attributes: {
    secured_against_address: debt.securedAgainstAddressId,
    loan_end_date: debt.loanEndDate
      ? debt.loanEndDate.formatForForm()
      : debt.loanEndDate,
    interest_rate: debt.interestRate,
    loan_product_end_date: debt.loanProductEndDate
      ? debt.loanProductEndDate.formatForForm()
      : debt.loanProductEndDate,
    monthly_payment: debt.monthlyPayment?.getValue() ?? null,
    product_type: 'personal_loan',
  },
  pending_valuations: debt.pendingValuations
    ? mapValuation(debt.pendingValuations)
    : [],
});

const creditCardToDto = (debt: CreditCard): CreditCardDTO => ({
  id: debt.id,
  client_ids: debt.clientIds,
  group_id: debt.groupId,
  type_id: debt.typeId,
  account_number: debt.accountNumber,
  provider_id: debt.providerId,
  adviser_id: debt.advisorId as number,
  attributes: {
    interest_rate: debt.interestRate,
    interest_rate_end_date: debt.interestRateEndDate
      ? debt.interestRateEndDate.formatForForm()
      : debt.interestRateEndDate,
    monthly_payment: debt.monthlyPayment?.getValue() ?? null,
    product_type: 'credit_card',
  },
  pending_valuations: debt.pendingValuations
    ? mapValuation(debt.pendingValuations)
    : [],
});

const otherDebtToDto = (debt: OtherDebt): OtherDebtDTO => ({
  id: debt.id,
  client_ids: debt.clientIds,
  group_id: debt.groupId,
  type_id: debt.typeId,
  account_number: debt.accountNumber,
  provider_id: debt.providerId,
  adviser_id: debt.advisorId as number,
  attributes: {
    interest_rate: debt.interestRate,
    interest_rate_end_date: debt.interestRateEndDate
      ? debt.interestRateEndDate.formatForForm()
      : debt.interestRateEndDate,
    monthly_payment: debt.monthlyPayment?.getValue() ?? null,
    product_type: 'other_debt',
  },
  pending_valuations: debt.pendingValuations
    ? mapValuation(debt.pendingValuations)
    : [],
});
