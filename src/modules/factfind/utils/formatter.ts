import { formatWithCurrency } from 'src/utils/money';
import { Valuation } from 'src/modules/factfind/models';
import { Property } from 'src/modules/factfind/types/Asset';

export const formatPropertyAssetAddress = (property: Property) => {
  return [
    property.addressLineOne,
    property.addressLineTwo,
    property.city,
    property.postCode,
  ]
    .filter((value) => value)
    .join(', ');
};

export const formatToString = (valuation: Valuation) => {
  return `${formatWithCurrency(
    valuation.amount,
  )}, ${valuation.date.formatToView()}`;
};

export const formatToMoney = (valuation: Valuation) => {
  return formatWithCurrency(valuation.amount, {
    maximumFractionDigits: 0,
  });
};
