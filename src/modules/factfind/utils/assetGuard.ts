import { useProducts } from 'src/composables/useProducts';
import {
  Account,
  Asset,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from 'src/modules/factfind/types/Asset';
import { Product } from 'src/modules/refdata/types/Product';

export class AssetTypeGuard {
  static isAssetProduct = function (
    asset: Asset,
    products: Product[],
  ): boolean {
    return products.map((_) => _.id).includes(asset.typeId);
  };

  static isAccount = (asset: Asset): asset is Account => {
    const { getAccountProducts } = useProducts();
    return this.isAssetProduct(asset, getAccountProducts.value);
  };

  static isProperty = (asset: Asset): asset is Property => {
    const { getPropertyProducts } = useProducts();
    return this.isAssetProduct(asset, getPropertyProducts.value);
  };

  static isCompanyShares = (asset: Asset): asset is CompanyShares => {
    const { getCompanySharesProducts } = useProducts();
    return this.isAssetProduct(asset, getCompanySharesProducts.value);
  };

  static isCryptoCurrency = (asset: Asset): asset is CryptoCurrency => {
    const { getCryptoCurrencyProducts } = useProducts();
    return this.isAssetProduct(asset, getCryptoCurrencyProducts.value);
  };

  static isOtherAsset = (asset: Asset): asset is OtherAsset => {
    const { getOtherAssetProducts } = useProducts();
    return this.isAssetProduct(asset, getOtherAssetProducts.value);
  };

  static isTermPolicy = (asset: Asset): asset is Account => {
    const { getTermPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getTermPolicyProducts.value);
  };

  static isIndemnityPolicy = (asset: Asset): asset is Account => {
    const { getIndemnityPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getIndemnityPolicyProducts.value);
  };

  static isWholeOfLifePolicy = (asset: Asset): asset is Account => {
    const { getWholeOfLifePolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getWholeOfLifePolicyProducts.value);
  };

  static isIncomeProtectionPolicy = (asset: Asset): asset is Account => {
    const { getIncomeProtectionPolicyProducts } = useProducts();
    return this.isAssetProduct(asset, getIncomeProtectionPolicyProducts.value);
  };

  static isDefinedBenefitPension = (
    asset: Asset,
  ): asset is DefinedBenefitPension => {
    const { getDefinedBenefitPensionProducts } = useProducts();
    return this.isAssetProduct(asset, getDefinedBenefitPensionProducts.value);
  };
}
