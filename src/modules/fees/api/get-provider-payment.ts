import { apiClient } from 'src/services/api';
import type { ProviderPayment } from 'src/modules/fees/models/payment';
import { DateTime } from 'src/utils/dateTime';
import { Money } from 'src/utils/money';
import {
  PaymentDTO,
  dtoStatusToDomain,
} from 'src/modules/fees/models/payment-dto';

interface DTO {
  id: number;
  name: string;
  payment_date: string;
  amount: string;
  notes: string;
  provider_id: number;
  status: PaymentDTO['status'];
}

export default async (paymentId: number): Promise<ProviderPayment> => {
  const dto = await apiClient.get<DTO>(
    `/api/v1/fees/provider-payments/${paymentId}`,
  );

  return {
    amount: new Money(+dto.amount),
    date: new DateTime(dto.payment_date),
    id: dto.id,
    name: dto.name,
    notes: dto.notes,
    counterpartyType: 'PROVIDER',
    providerId: dto.provider_id,
    status: dtoStatusToDomain(dto.status),
  };
};
