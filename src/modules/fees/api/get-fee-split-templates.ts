import { array, boolean, number, object, string } from 'yup';
import { apiClient } from 'src/services/api';
import { QueryParams } from 'src/types/api';
import { FeeSplitTemplateList } from 'src/modules/fees/models/fee-split-template';

const getFeeSplitTemplatesValidationSchema = object({
  fee_split_templates: array()
    .of(
      object({
        id: number().required(),
        template_name: string().required(),
        administrator_id: number().required(),
        administrator_name: string().required(),
        is_active: boolean().required().defined(),
        linked_accounts_count: number().required(),
      }).required(),
    )
    .required(),
  total_count: number().required(),
});

export default async (
  queryParams?: QueryParams,
): Promise<FeeSplitTemplateList> => {
  const response = await apiClient.get<Promise<unknown>>(
    '/api/v1/fee-split-templates',
    queryParams,
  );

  const feeSplitTemplatesDTO =
    await getFeeSplitTemplatesValidationSchema.validate(response);

  return {
    items: feeSplitTemplatesDTO.fee_split_templates.map((template) => ({
      id: template.id,
      adviserId: template.administrator_id,
      adviserName: template.administrator_name,
      isActive: template.is_active,
      linkedAccounts: template.linked_accounts_count,
      name: template.template_name,
    })),
    totalItems: feeSplitTemplatesDTO.total_count,
  };
};
