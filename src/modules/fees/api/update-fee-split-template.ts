import { apiClient } from 'src/services/api';

interface Body {
  template_name: string;
  is_active: boolean;
  administrator_id: number;
  lines: Array<{
    administrator_id: number;
    role: string;
    type: string;
    split_initial: string;
    split_ongoing: string;
    is_payable: boolean;
  }>;
}

export default async (templateId: number, body: Body): Promise<void> => {
  await apiClient.put<Body, Promise<void>>(
    `/api/v1/fee-split-templates/${templateId}`,
    body,
  );
};
