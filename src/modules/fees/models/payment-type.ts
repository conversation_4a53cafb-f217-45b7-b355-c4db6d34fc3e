import { SelectOption } from 'src/components/form/fields/field-model';

export interface IPaymentType {
  toLabel: () => string;
  toValue: () => PaymentTypeEnum;
}

export enum PaymentTypeEnum {
  InitialFee = 'InitialFee',
  InitialCommission = 'InitialCommission',
  OngoingFee = 'OngoingFee',
  OngoingCommission = 'OngoingCommission',
}

const paymentTypeMap = new Map<PaymentTypeEnum, string>([
  [PaymentTypeEnum.InitialFee, 'Initial fee'],
  [PaymentTypeEnum.InitialCommission, 'Initial commission'],
  [PaymentTypeEnum.OngoingFee, 'Ongoing fee'],
  [PaymentTypeEnum.OngoingCommission, 'Ongoing commission'],
]);

export class PaymentType implements IPaymentType {
  constructor(private readonly value: PaymentTypeEnum) {}

  toLabel(): string {
    return paymentTypeMap.get(this.value) as string;
  }

  toValue(): PaymentTypeEnum {
    return this.value;
  }
}

const paymentTypesInstances = [
  new PaymentType(PaymentTypeEnum.InitialFee),
  new PaymentType(PaymentTypeEnum.InitialCommission),
  new PaymentType(PaymentTypeEnum.OngoingFee),
  new PaymentType(PaymentTypeEnum.OngoingCommission),
];

export const paymentTypeOptions = (): SelectOption[] => {
  return paymentTypesInstances.map((instance) => ({
    value: instance.toValue(),
    label: instance.toLabel(),
  }));
};
