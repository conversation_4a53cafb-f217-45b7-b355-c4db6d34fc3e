import { IDateTime } from 'src/utils/dateTime';
import { Paginable } from 'src/types/Pagination';
import {
  IPaymentStatus,
  PaymentStatusEnum,
} from 'src/modules/fees/models/payment-status';
import { IMoney } from 'src/utils/money';
import { Client } from 'src/modules/clients';

export interface ModelFactor {
  id: number;
  date: IDateTime;
  name: string;
  amount: IMoney;
  status: IPaymentStatus;
  notes: string;
  clientId: PaymentClientId;
  providerId: PaymentProviderId;
  counterpartyType: 'CLIENT' | 'PROVIDER';
}

export interface PaymentList extends Paginable<PaymentListItem> {}

export interface PaymentListItem
  extends Pick<
    ModelFactor,
    'id' | 'amount' | 'date' | 'name' | 'status' | 'counterpartyType'
  > {}

export type PaymentClientId = Client['id'];
export type PaymentProviderId = number;

export interface ClientPayment
  extends Pick<
    ModelFactor,
    | 'id'
    | 'name'
    | 'amount'
    | 'notes'
    | 'date'
    | 'counterpartyType'
    | 'clientId'
    | 'status'
  > {}

export interface ProviderPayment
  extends Pick<
    ModelFactor,
    | 'id'
    | 'name'
    | 'amount'
    | 'notes'
    | 'date'
    | 'counterpartyType'
    | 'providerId'
    | 'status'
  > {}

export const canBeEdited = (payment: { status: IPaymentStatus }) => {
  return payment.status.is(PaymentStatusEnum.Unmatched);
};

export const canBeDeleted = (payment: { status: IPaymentStatus }) => {
  return payment.status.is(PaymentStatusEnum.Unmatched);
};

export const canBeViewed = (payment: { status: IPaymentStatus }) => {
  return payment.status && !payment.status.is(PaymentStatusEnum.Unmatched);
};
