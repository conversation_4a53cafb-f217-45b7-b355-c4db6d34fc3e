import {
  IPaymentStatus,
  PaymentStatus,
  PaymentStatusEnum,
} from 'src/modules/fees/models/payment-status';

export interface PaymentDTO {
  id: number;
  client_first_name: string;
  client_last_name: string;
  payment_date: string;
  counterparty_name: string;
  counterparty_type: 'CLIENT' | 'PROVIDER';
  amount: string;
  provider_name: string;
  status: PaymentStatusDTOEnum;
}

export interface CreateClientPaymentDTO extends Pick<PaymentDTO, 'id'> {}

export interface CreateProviderPaymentDTO extends Pick<PaymentDTO, 'id'> {}

enum PaymentStatusDTOEnum {
  Matched = 'Matched',
  Unmatched = 'Unmatched',
  MatchedAndUnpaid = 'MatchedAndUnpaid',
  MatchedAndPaid = 'MatchedAndPaid',
  MatchedPartPaid = 'MatchedPartPaid',
}

export const dtoStatusToDomain = (
  status: PaymentDTO['status'],
): IPaymentStatus => {
  const map = new Map<PaymentDTO['status'], PaymentStatusEnum>([
    [PaymentStatusDTOEnum.Matched, PaymentStatusEnum.Matched],
    [PaymentStatusDTOEnum.Unmatched, PaymentStatusEnum.Unmatched],
    [PaymentStatusDTOEnum.MatchedAndUnpaid, PaymentStatusEnum.MatchedAndUnpaid],
    [
      PaymentStatusDTOEnum.MatchedAndPaid,
      PaymentStatusEnum.MatchedAndFullyPaid,
    ],
    [PaymentStatusDTOEnum.MatchedPartPaid, PaymentStatusEnum.MatchedPartPaid],
  ]);
  return new PaymentStatus(map.get(status) as PaymentStatusEnum);
};

export const domainStatusToDto = (
  paymentStatus: IPaymentStatus,
): PaymentDTO['status'] => {
  const map = new Map<PaymentStatusEnum, PaymentDTO['status']>([
    [PaymentStatusEnum.Matched, PaymentStatusDTOEnum.Matched],
    [PaymentStatusEnum.Unmatched, PaymentStatusDTOEnum.Unmatched],
    [
      PaymentStatusEnum.MatchedAndFullyPaid,
      PaymentStatusDTOEnum.MatchedAndPaid,
    ],
    [PaymentStatusEnum.MatchedAndUnpaid, PaymentStatusDTOEnum.MatchedAndUnpaid],
    [PaymentStatusEnum.MatchedPartPaid, PaymentStatusDTOEnum.MatchedPartPaid],
  ]);

  return map.get(paymentStatus.toValue()) as PaymentStatusDTOEnum;
};
