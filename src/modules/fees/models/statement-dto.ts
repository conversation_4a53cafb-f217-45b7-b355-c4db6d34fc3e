import {
  IStatementStatus,
  StatementStatus,
  StatementStatusEnum,
} from 'src/modules/fees/models/statement-status';
import {
  IPaymentType,
  PaymentType,
  PaymentTypeEnum,
} from 'src/modules/fees/models/payment-type';

export interface StatementDTO {
  id: number;
  client_first_name: string;
  client_last_name: string;
  statement_date: string;
  counterparty_name: string;
  counterparty_type: 'CLIENT' | 'PROVIDER';
  total_amount: number;
  lines_count_paid: number;
  lines_count_unpaid: number;
  lines_count_total: number;
  provider_name: string;
  status: StatementStatusDTOEnum;
  type: number;
}

export interface CreateClientStatementDTO extends Pick<StatementDTO, 'id'> {}

export interface CreateProviderStatementDTO extends Pick<StatementDTO, 'id'> {}

enum StatementStatusDTOEnum {
  All = 'All',
  Matched = 'Matched',
  Unmatched = 'Unmatched',
  MatchedAndUnpaid = 'MatchedAndUnpaid',
  MatchedAndFullyPaid = 'MatchedAndPaid',
  PartPaid = 'MatchedPartPaid',
}

export const dtoStatusToDomain = (
  status: StatementDTO['status'],
): IStatementStatus => {
  const map = new Map<StatementDTO['status'], StatementStatusEnum>([
    [StatementStatusDTOEnum.Matched, StatementStatusEnum.Matched],
    [StatementStatusDTOEnum.Unmatched, StatementStatusEnum.Unmatched],
    [
      StatementStatusDTOEnum.MatchedAndUnpaid,
      StatementStatusEnum.MatchedAndUnpaid,
    ],
    [StatementStatusDTOEnum.PartPaid, StatementStatusEnum.PartPaid],
    [
      StatementStatusDTOEnum.MatchedAndFullyPaid,
      StatementStatusEnum.MatchedAndFullyPaid,
    ],
  ]);
  return new StatementStatus(map.get(status) as StatementStatusEnum);
};

export const domainStatusToDto = (
  statementStatus: IStatementStatus,
): StatementDTO['status'] => {
  const map = new Map<StatementStatusEnum, StatementDTO['status']>([
    [StatementStatusEnum.Matched, StatementStatusDTOEnum.Matched],
    [StatementStatusEnum.Unmatched, StatementStatusDTOEnum.Unmatched],
    [
      StatementStatusEnum.MatchedAndUnpaid,
      StatementStatusDTOEnum.MatchedAndUnpaid,
    ],
    [StatementStatusEnum.PartPaid, StatementStatusDTOEnum.PartPaid],
    [
      StatementStatusEnum.MatchedAndFullyPaid,
      StatementStatusDTOEnum.MatchedAndFullyPaid,
    ],
  ]);

  return map.get(statementStatus.toValue()) as StatementStatusDTOEnum;
};

export const domainPaymentTypeToDto = (
  paymentType: IPaymentType,
): StatementDTO['type'] => {
  const map = new Map<PaymentTypeEnum, StatementDTO['type']>([
    [PaymentTypeEnum.InitialFee, 1],
    [PaymentTypeEnum.InitialCommission, 2],
    [PaymentTypeEnum.OngoingFee, 3],
    [PaymentTypeEnum.OngoingCommission, 4],
  ]);

  return map.get(paymentType.toValue()) as number;
};

export const dtoPaymentTypeToDomain = (
  paymentType: StatementDTO['type'],
): IPaymentType => {
  const map = new Map<StatementDTO['type'], IPaymentType>([
    [1, new PaymentType(PaymentTypeEnum.InitialFee)],
    [2, new PaymentType(PaymentTypeEnum.InitialCommission)],
    [3, new PaymentType(PaymentTypeEnum.OngoingFee)],
    [4, new PaymentType(PaymentTypeEnum.OngoingCommission)],
  ]);

  return map.get(paymentType) as IPaymentType;
};
