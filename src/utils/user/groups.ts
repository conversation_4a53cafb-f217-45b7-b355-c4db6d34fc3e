import { fetchAuthSession } from 'aws-amplify/auth';
import { UserRole } from 'src/modules/auth';

export const getUserGroups = async (): Promise<UserRole[]> => {
  const { accessToken } = (await fetchAuthSession()).tokens ?? {};
  return (accessToken?.payload['cognito:groups'] as UserRole[]) ?? [];
};

const is = async (role: UserRole) => {
  let _groups: string[] = [];
  _groups = await getUserGroups();
  return _groups.includes(role);
};

export const isClient = async (): Promise<boolean> => await is(UserRole.Client);
export const isAdvisor = async (): Promise<boolean> =>
  await is(UserRole.Adviser);
export const isIntroducer = async (): Promise<boolean> =>
  await is(UserRole.Introducer);
export const isRelationshipManager = async (): Promise<boolean> =>
  await is(UserRole.RelationshipManager);
export const isCompliance = async (): Promise<boolean> =>
  await is(UserRole.Compliance);
export const isCaseManagement = async (): Promise<boolean> =>
  await is(UserRole.CaseManagement);
export const isParaplanner = async (): Promise<boolean> =>
  await is(UserRole.Paraplanner);
export const isSuperAdmin = async (): Promise<boolean> =>
  await is(UserRole.SuperAdmin);
