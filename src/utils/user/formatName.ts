import { Person } from 'src/types/Person';

type PersonNameable = Omit<Person, 'id'>;
type People = Array<Record<string, any> & PersonNameable>;

export const formatName = ({ firstName, lastName }: PersonNameable): string => {
  if (lastName == null) return firstName;
  return [firstName, lastName].join(' ');
};

export const formatNames = (people: People, delimiter = ', ') => {
  return people.map((person) => formatName(person)).join(delimiter);
};
