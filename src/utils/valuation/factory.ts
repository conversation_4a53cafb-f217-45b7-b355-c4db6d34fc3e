import { Nullable } from 'src/types/Common';
import { Money } from 'src/utils/money';
import { DateTime } from 'src/utils/dateTime';
import { ValuationType } from 'src/modules/factfind/types';
import { Valuation } from 'src/modules/factfind/models';

export function factory(
  amount: Nullable<number>,
  date: Nullable<string | Date>,
  valuationType: Nullable<ValuationType>,
): Valuation | null {
  if (!amount || !date || !valuationType) {
    return null;
  }

  return {
    amount: new Money(+amount),
    date: new DateTime(date),
    type: valuationType,
  };
}
